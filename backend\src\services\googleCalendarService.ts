import { google, Auth, calendar_v3 } from 'googleapis';
import { IUser } from '../models/User';
import { ITask } from '../models/Task';
import secureLogger from '../utils/secureLogger';

const GOOGLE_CALENDAR_CLIENT_ID = process.env.GOOGLE_CALENDAR_CLIENT_ID;
const GOOGLE_CALENDAR_CLIENT_SECRET = process.env.GOOGLE_CALENDAR_CLIENT_SECRET;
const GOOGLE_CALENDAR_REDIRECT_URI = `${process.env.API_BASE_URL}/api/auth/google/callback/calendar`;
const FRONTEND_URL = process.env.FRONTEND_URL;

class GoogleCalendarService {
  private getOAuth2Client(userTokens?: IUser['googleCalendarTokens']): Auth.OAuth2Client {
    const oauth2Client = new google.auth.OAuth2(
      GOOGLE_CALENDAR_CLIENT_ID,
      GOOGLE_CALENDAR_CLIENT_SECRET,
      GOOG<PERSON>_CALENDAR_REDIRECT_URI
    );

    if (userTokens?.accessToken) {
      oauth2Client.setCredentials({
        access_token: userTokens.accessToken,
        refresh_token: userTokens.refreshToken,
        expiry_date: userTokens.expiryDate,
        scope: userTokens.scope,
      });
    }
    return oauth2Client;
  }

  private async getCalendarApi(user: IUser): Promise<calendar_v3.Calendar | null> {
    if (!user.googleCalendarTokens?.accessToken) {
      secureLogger.warn(`[GoogleCalendarService] User ${user._id} has no Google Calendar access token.`);
      return null;
    }

    const oauth2Client = this.getOAuth2Client(user.googleCalendarTokens);

    // Handle token refresh if needed
    if (user.googleCalendarTokens.expiryDate && user.googleCalendarTokens.expiryDate < Date.now() + 60000) {
      if (user.googleCalendarTokens.refreshToken) {
        try {
          secureLogger.log(`[GoogleCalendarService] Refreshing Google Calendar token for user ${user._id}`);
          const { tokens } = await oauth2Client.refreshAccessToken();
          oauth2Client.setCredentials(tokens);
          
          // Update user's tokens in DB
          user.googleCalendarTokens.accessToken = tokens.access_token!;
          if (tokens.refresh_token) user.googleCalendarTokens.refreshToken = tokens.refresh_token;
          user.googleCalendarTokens.expiryDate = tokens.expiry_date!;
          await user.save();
          
          secureLogger.log(`[GoogleCalendarService] Token refreshed and saved for user ${user._id}`);
        } catch (refreshError: any) {
          secureLogger.error(`[GoogleCalendarService] Failed to refresh Google Calendar token for user ${user._id}:`, refreshError);
          user.googleCalendarSyncEnabled = false;
          user.googleCalendarTokens = undefined;
          await user.save();
          return null;
        }
      } else {
        secureLogger.warn(`[GoogleCalendarService] Access token expired for user ${user._id}, but no refresh token available.`);
        user.googleCalendarSyncEnabled = false;
        user.googleCalendarTokens = undefined;
        await user.save();
        return null;
      }
    }

    return google.calendar({ version: 'v3', auth: oauth2Client });
  }

  async createEvent(user: IUser, task: ITask): Promise<string | null> {
    const calendar = await this.getCalendarApi(user);
    if (!calendar) return null;

    try {
      // Convert task deadline to event time
      const startTime = new Date(task.deadline);
      const endTime = new Date(startTime.getTime() + 60 * 60 * 1000); // Default 1 hour duration

      const event = {
        summary: task.title,
        description: `${task.content}\n\nView in Task OrganAIzer: ${FRONTEND_URL}/task/${task._id}`,
        start: {
          dateTime: startTime.toISOString(),
          timeZone: 'UTC',
        },
        end: {
          dateTime: endTime.toISOString(),
          timeZone: 'UTC',
        },
        reminders: {
          useDefault: true,
        },
      };

      const response = await calendar.events.insert({
        calendarId: user.googleCalendarId || 'primary',
        requestBody: event,
      });

      if (response.data.id) {
        secureLogger.log(`[GoogleCalendarService] Created event ${response.data.id} for task ${task._id}`);
        return response.data.id;
      }
      return null;
    } catch (error: any) {
      secureLogger.error(`[GoogleCalendarService] Failed to create event for task ${task._id}:`, error);
      return null;
    }
  }

  async updateEvent(user: IUser, task: ITask, eventId: string): Promise<boolean> {
    const calendar = await this.getCalendarApi(user);
    if (!calendar) return false;

    try {
      // Convert task deadline to event time
      const startTime = new Date(task.deadline);
      const endTime = new Date(startTime.getTime() + 60 * 60 * 1000);

      const event = {
        summary: task.title,
        description: `${task.content}\n\nView in Task OrganAIzer: ${FRONTEND_URL}/task/${task._id}`,
        start: {
          dateTime: startTime.toISOString(),
          timeZone: 'UTC',
        },
        end: {
          dateTime: endTime.toISOString(),
          timeZone: 'UTC',
        },
      };

      await calendar.events.update({
        calendarId: user.googleCalendarId || 'primary',
        eventId: eventId,
        requestBody: event,
      });

      secureLogger.log(`[GoogleCalendarService] Updated event ${eventId} for task ${task._id}`);
      return true;
    } catch (error: any) {
      secureLogger.error(`[GoogleCalendarService] Failed to update event ${eventId} for task ${task._id}:`, error);
      return false;
    }
  }

  async deleteEvent(user: IUser, eventId: string): Promise<boolean> {
    const calendar = await this.getCalendarApi(user);
    if (!calendar) return false;

    try {
      await calendar.events.delete({
        calendarId: user.googleCalendarId || 'primary',
        eventId: eventId,
      });

      secureLogger.log(`[GoogleCalendarService] Deleted event ${eventId}`);
      return true;
    } catch (error: any) {
      secureLogger.error(`[GoogleCalendarService] Failed to delete event ${eventId}:`, error);
      return false;
    }
  }

  async getAuthUrl(): Promise<string> {
    const oauth2Client = this.getOAuth2Client();
    const scopes = [
      'https://www.googleapis.com/auth/calendar.events',
      'https://www.googleapis.com/auth/calendar',
    ];

    return oauth2Client.generateAuthUrl({
      access_type: 'offline',
      scope: scopes,
      prompt: 'consent',
    });
  }

  async handleAuthCallback(code: string): Promise<Auth.Credentials | null> {
    const oauth2Client = this.getOAuth2Client();

    try {
      const { tokens } = await oauth2Client.getToken(code);
      return tokens;
    } catch (error: any) {
      secureLogger.error('[GoogleCalendarService] Failed to get tokens from auth code:', error);
      return null;
    }
  }
}

export const googleCalendarService = new GoogleCalendarService();
