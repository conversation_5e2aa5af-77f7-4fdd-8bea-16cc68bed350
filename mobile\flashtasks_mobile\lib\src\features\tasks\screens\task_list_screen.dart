import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:gap/gap.dart';
import 'package:lucide_icons/lucide_icons.dart';
import 'package:go_router/go_router.dart';
import '../providers/task_provider.dart';
import '../widgets/task_item.dart';
import '../widgets/task_creation_bar.dart';
import '../models/pagination_meta.dart';
import '../../../shared/widgets/error_display.dart';

class TaskListScreen extends ConsumerStatefulWidget {
  const TaskListScreen({super.key});

  @override
  ConsumerState<TaskListScreen> createState() => _TaskListScreenState();
}

class _TaskListScreenState extends ConsumerState<TaskListScreen> {
  @override
  void initState() {
    super.initState();
    // Force load tasks on initialization
    Future.microtask(() => ref.read(taskProvider.notifier).refresh());
  }

  @override
  Widget build(BuildContext context) {
    final taskState = ref.watch(taskProvider);
    final theme = Theme.of(context);    return Column(
      children: [
        // TEMP: Button to test notifications - commented out as no longer needed
        // Padding(
        //   padding: const EdgeInsets.all(8.0),
        //   child: ElevatedButton(
        //     onPressed: () {
        //       context.go('/notification-test');
        //     },
        //     child: const Text('Test Notifications'),
        //   ),
        // ),
        // Task Creation Bar
        const TaskCreationBar(),
        // Task List
        Expanded(
          child: RefreshIndicator(
            color: theme.colorScheme.primary,
            onRefresh: () => ref.read(taskProvider.notifier).refresh(),
            child: SingleChildScrollView(
              physics: const AlwaysScrollableScrollPhysics(),
              child: Column(
                children: [
                  // Task List
                  _buildTaskContent(context, taskState, ref),

                  // Add padding at the bottom
                  const SizedBox(height: 80),
                ],
              ),
            ),
          ),
        ),

        // AI Insights in a separate tab or screen
        // We'll implement this in a future update
      ],
    );
  }

  Widget _buildTaskContent(BuildContext context, TaskState taskState, WidgetRef ref) {
    final theme = Theme.of(context);

    // Loading state
    if (taskState.isLoading && taskState.tasks.isEmpty) {
      return Center(
        child: Padding(
          padding: const EdgeInsets.symmetric(vertical: 40),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              CircularProgressIndicator(
                color: theme.colorScheme.primary,
              ),
              const SizedBox(height: 16),
              const Text('Loading tasks...'),
            ],
          ),
        ),
      );
    }

    // Error state
    if (taskState.error != null && taskState.tasks.isEmpty) {
      return Padding(
        padding: const EdgeInsets.symmetric(vertical: 40),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            ErrorDisplay(
              message: taskState.error!,
              onRetry: () => ref.read(taskProvider.notifier).refresh(),
            ),
          ],
        ),
      );
    }

    // Empty state
    if (taskState.tasks.isEmpty) {
      return _buildEmptyState(context);
    }

    // Task list with fixed height constraints
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Task list header
        Padding(
          padding: const EdgeInsets.fromLTRB(16, 16, 16, 8),
          child: Text(
            'Your Tasks',
            style: theme.textTheme.titleLarge,
          ),
        ),

        // Task list items with shrinkWrap to avoid scrolling conflicts
        ListView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          // Always add a pagination control item, regardless of hasMore
          itemCount: taskState.tasks.length + 1,
          itemBuilder: (context, index) {
            // Show pagination controls at the end
            if (index == taskState.tasks.length) {
              return _buildPaginationControls(context, taskState, ref);
            }

            // Show task item
            final task = taskState.tasks[index];
            return TaskItem(
              key: ValueKey(task.id), // Add key for better widget identification
              task: task,
              onToggleCompletion: () => _toggleTaskCompletion(ref, task.id),
              onDelete: () => _deleteTask(ref, task.id),
              onEdit: (task) => _navigateToEditTask(context, task.id),
            );
          },
        ),
      ],
    );
  }

  Widget _buildEmptyState(BuildContext context) {
    final theme = Theme.of(context);

    return Center(
      child: Padding(
        padding: const EdgeInsets.all(24.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              LucideIcons.clipboardCheck,
              size: 64,
              color: theme.colorScheme.primary.withAlpha(128), // 0.5 opacity
            ),
            const Gap(24),
            Text(
              'No tasks found',
              style: theme.textTheme.headlineSmall,
              textAlign: TextAlign.center,
            ),
            const Gap(12),
            Text(
              'Tap + to add one!',
              style: theme.textTheme.bodyLarge?.copyWith(
                color: theme.colorScheme.onSurface.withAlpha(178), // 0.7 opacity
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPaginationControls(BuildContext context, TaskState taskState, WidgetRef ref) {
    final theme = Theme.of(context);
    final currentPage = taskState.currentPage ?? 1;
    final totalPages = taskState.totalPages ?? 1;

    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 16.0),
      child: Column(
        children: [
          if (taskState.isLoading)
            const CircularProgressIndicator()
          else
            Column(
              children: [
                // Page navigation controls
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    IconButton(
                      icon: const Icon(Icons.chevron_left),
                      onPressed: currentPage > 1
                          ? () => ref.read(taskProvider.notifier).goToPage(currentPage - 1)
                          : null,
                      tooltip: 'Previous page',
                    ),
                    Text(
                      '$currentPage / $totalPages',
                      style: theme.textTheme.bodyMedium,
                    ),
                    IconButton(
                      icon: const Icon(Icons.chevron_right),
                      onPressed: currentPage < totalPages
                          ? () => ref.read(taskProvider.notifier).goToPage(currentPage + 1)
                          : null,
                      tooltip: 'Next page',
                    ),
                  ],
                ),
                // Page size selector
                Padding(
                  padding: const EdgeInsets.only(top: 8.0),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text(
                        'Items per page: ',
                        style: theme.textTheme.bodySmall,
                      ),
                      const SizedBox(width: 8),
                      ...PaginationMeta.pageSizeOptions.map((size) => _buildPageSizeOption(
                        context,
                        size,
                        taskState.pageSize,
                        ref,
                      )),
                    ],
                  ),
                ),
              ],
            ),
        ],
      ),
    );
  }

  // Helper method to build a single page size option button
  Widget _buildPageSizeOption(BuildContext context, int size, int currentSize, WidgetRef ref) {
    final theme = Theme.of(context);
    final isSelected = size == currentSize;

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 4),
      child: InkWell(
        onTap: isSelected ? null : () => ref.read(taskProvider.notifier).updatePageSize(size),
        borderRadius: BorderRadius.circular(4),
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
          decoration: BoxDecoration(
            color: isSelected ? theme.colorScheme.primary : Colors.transparent,
            border: Border.all(color: theme.colorScheme.primary),
            borderRadius: BorderRadius.circular(4),
          ),
          child: Text(
            size.toString(),
            style: theme.textTheme.bodySmall?.copyWith(
              color: isSelected ? theme.colorScheme.onPrimary : theme.colorScheme.primary,
            ),
          ),
        ),
      ),
    );
  }

  void _toggleTaskCompletion(WidgetRef ref, String taskId) async {
    try {
      await ref.read(taskProvider.notifier).toggleTaskCompletion(taskId);
    } catch (e) {
      // Show error message to user if still mounted
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to update task: $e'),
            backgroundColor: Theme.of(context).colorScheme.error,
          ),
        );
      }
    }
  }

  void _deleteTask(WidgetRef ref, String taskId) async {
    // Show a loading indicator
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Row(
            children: [
              SizedBox(
                width: 20,
                height: 20,
                child: CircularProgressIndicator(strokeWidth: 2.0)
              ),
              SizedBox(width: 16),
              Text('Deleting task...'),
            ],
          ),
          duration: Duration(seconds: 2),
        ),
      );
    }

    try {
      await ref.read(taskProvider.notifier).deleteTask(taskId);

      // Show success message to user
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Task deleted successfully'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      // Show error message to user
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to delete task: $e'),
            backgroundColor: Theme.of(context).colorScheme.error,
          ),
        );
      }
    }
  }

  void _navigateToEditTask(BuildContext context, String taskId) {
    // Navigate to the task form screen in edit mode
    try {
      print('Navigating to edit task with ID: $taskId');
      context.goNamed(
        'editTask', // This route name needs to be defined in app_router.dart
        pathParameters: {'taskId': taskId},
      );
    } catch (e) {
      print('Error navigating to edit task: $e');
      // Fallback navigation using path
      context.go('/dashboard/home/<USER>/$taskId/edit');
    }
  }
}