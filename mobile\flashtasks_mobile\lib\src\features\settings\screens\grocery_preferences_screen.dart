import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:lucide_icons/lucide_icons.dart';
import '../providers/grocery_preferences_provider.dart';
import '../models/grocery_preferences.dart';
import '../../groceries/providers/grocery_provider.dart';

/// Screen for managing grocery preferences
class GroceryPreferencesScreen extends ConsumerStatefulWidget {
  const GroceryPreferencesScreen({super.key});

  @override
  ConsumerState<GroceryPreferencesScreen> createState() => _GroceryPreferencesScreenState();
}

class _GroceryPreferencesScreenState extends ConsumerState<GroceryPreferencesScreen> {
  bool _isSaving = false;

  @override
  void initState() {
    super.initState();
    // Load shared lists when screen opens
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(groceryProvider.notifier).fetchSharedLists();
    });
  }

  Future<void> _handleDefaultListTypeChange(String value) async {
    setState(() => _isSaving = true);

    try {
      final groceryState = ref.read(groceryProvider);

      if (value == 'personal') {
        await ref.read(groceryPreferencesProvider.notifier).setDefaultToPersonal();
      } else if (value == 'shared') {
        // If switching to shared but no shared list selected, use first available
        if (groceryState.sharedLists.isNotEmpty) {
          final firstSharedList = groceryState.sharedLists.first;
          final listOwnerId = firstSharedList.userId;
          await ref.read(groceryPreferencesProvider.notifier).setDefaultToSharedList(listOwnerId);
        } else {
          // Show error if no shared lists available
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('No shared lists available. You need access to at least one shared list.'),
                backgroundColor: Colors.red,
              ),
            );
          }
          return;
        }
      }

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Default list type updated successfully'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to update default list type: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isSaving = false);
      }
    }
  }

  Future<void> _handleSharedListChange(String listOwnerId) async {
    setState(() => _isSaving = true);

    try {
      await ref.read(groceryPreferencesProvider.notifier).setDefaultToSharedList(listOwnerId);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Default shared list updated successfully'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to update default shared list: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isSaving = false);
      }
    }
  }

  Future<void> _handleAutoSwitchToggle(bool enabled) async {
    setState(() => _isSaving = true);

    try {
      await ref.read(groceryPreferencesProvider.notifier).toggleAutoSwitchToDefault(enabled);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Auto-switch ${enabled ? 'enabled' : 'disabled'} successfully'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to update auto-switch setting: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isSaving = false);
      }
    }
  }

  Future<void> _handleShowPersonalToggle(bool show) async {
    setState(() => _isSaving = true);

    try {
      await ref.read(groceryPreferencesProvider.notifier).toggleShowPersonalListInSidebar(show);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Personal list visibility ${show ? 'enabled' : 'disabled'} successfully'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to update personal list visibility: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isSaving = false);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final preferencesState = ref.watch(groceryPreferencesProvider);
    final groceryState = ref.watch(groceryProvider);
    final theme = Theme.of(context);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Grocery Preferences'),
        backgroundColor: theme.colorScheme.surface,
        elevation: 1,
      ),
      body: preferencesState.isLoading && preferencesState.preferences == null
          ? const Center(child: CircularProgressIndicator())
          : RefreshIndicator(
              onRefresh: () => ref.read(groceryPreferencesProvider.notifier).refreshPreferences(),
              child: ListView(
                padding: const EdgeInsets.all(16),
                children: [
                  _buildDefaultListCard(preferencesState.preferences, groceryState),
                  const SizedBox(height: 16),
                  _buildBehaviorCard(preferencesState.preferences),
                  const SizedBox(height: 16),
                  _buildQuickAddInfoCard(),
                ],
              ),
            ),
    );
  }

  Widget _buildDefaultListCard(GroceryPreferences? preferences, GroceryState groceryState) {
    final theme = Theme.of(context);
    final currentDefaultList = preferences?.defaultListType ?? 'personal';
    final currentSharedListId = preferences?.defaultSharedListOwnerId;

    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  LucideIcons.shoppingCart,
                  color: theme.colorScheme.primary,
                  size: 24,
                ),
                const SizedBox(width: 12),
                Text(
                  'Default Grocery List',
                  style: theme.textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Text(
              'Choose which grocery list to use by default when adding items',
              style: theme.textTheme.bodyMedium?.copyWith(
                color: theme.colorScheme.onSurfaceVariant,
              ),
            ),
            const SizedBox(height: 16),

            // Default List Type Dropdown
            DropdownButtonFormField<String>(
              value: currentDefaultList,
              decoration: InputDecoration(
                labelText: 'Default List Type',
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
                prefixIcon: Icon(
                  currentDefaultList == 'personal' ? LucideIcons.user : LucideIcons.users,
                  size: 20,
                ),
              ),
              items: [
                const DropdownMenuItem(
                  value: 'personal',
                  child: Row(
                    children: [
                      Icon(LucideIcons.user, size: 20),
                      SizedBox(width: 8),
                      Text('My Personal List'),
                    ],
                  ),
                ),
                DropdownMenuItem(
                  value: 'shared',
                  enabled: groceryState.sharedLists.isNotEmpty,
                  child: Row(
                    children: [
                      Icon(
                        LucideIcons.users,
                        size: 20,
                        color: groceryState.sharedLists.isEmpty
                            ? theme.disabledColor
                            : null,
                      ),
                      const SizedBox(width: 8),
                      Text(
                        groceryState.sharedLists.isEmpty
                            ? 'Shared List (No shared lists available)'
                            : 'Shared List',
                        style: TextStyle(
                          color: groceryState.sharedLists.isEmpty
                              ? theme.disabledColor
                              : null,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
              onChanged: _isSaving ? null : (String? value) {
                if (value != null) {
                  _handleDefaultListTypeChange(value);
                }
              },
            ),

            // Shared List Selection (only show if shared is selected)
            if (currentDefaultList == 'shared' && groceryState.sharedLists.isNotEmpty) ...[
              const SizedBox(height: 16),
              DropdownButtonFormField<String>(
                value: currentSharedListId,
                decoration: InputDecoration(
                  labelText: 'Select Shared List',
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                  prefixIcon: const Icon(LucideIcons.list, size: 20),
                ),
                items: groceryState.sharedLists.map((list) {
                  return DropdownMenuItem(
                    value: list.userId,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Text(
                          list.name ?? "Shared List",
                          style: theme.textTheme.bodyMedium?.copyWith(
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                        Text(
                          'Role: ${list.userRole?.name ?? "Unknown"}',
                          style: theme.textTheme.bodySmall?.copyWith(
                            color: theme.colorScheme.onSurfaceVariant,
                          ),
                        ),
                      ],
                    ),
                  );
                }).toList(),
                onChanged: _isSaving ? null : (value) {
                  if (value != null) {
                    _handleSharedListChange(value);
                  }
                },
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildBehaviorCard(GroceryPreferences? preferences) {
    final theme = Theme.of(context);
    final autoSwitch = preferences?.autoSwitchToDefault ?? false;
    final showPersonal = preferences?.showPersonalListInSidebar ?? true;

    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  LucideIcons.settings,
                  color: theme.colorScheme.primary,
                  size: 24,
                ),
                const SizedBox(width: 12),
                Text(
                  'Behavior Settings',
                  style: theme.textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),

            // Auto-switch to Default
            SwitchListTile(
              title: const Text('Auto-switch to Default List'),
              subtitle: const Text('Automatically switch to your default list when opening the grocery page'),
              value: autoSwitch,
              onChanged: _isSaving ? null : _handleAutoSwitchToggle,
              secondary: const Icon(LucideIcons.repeat),
            ),

            const Divider(),

            // Show Personal List in Sidebar
            SwitchListTile(
              title: const Text('Show Personal List in Sidebar'),
              subtitle: const Text('Display your personal list in the sidebar navigation'),
              value: showPersonal,
              onChanged: _isSaving ? null : _handleShowPersonalToggle,
              secondary: const Icon(LucideIcons.eye),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildQuickAddInfoCard() {
    final theme = Theme.of(context);

    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  LucideIcons.info,
                  color: theme.colorScheme.primary,
                  size: 24,
                ),
                const SizedBox(width: 12),
                Text(
                  'Quick Add & AI Suggestions',
                  style: theme.textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Text(
              'Items added via quick add and AI suggestions will automatically go to your default list or the currently active list based on your preferences.',
              style: theme.textTheme.bodyMedium?.copyWith(
                color: theme.colorScheme.onSurfaceVariant,
              ),
            ),
            const SizedBox(height: 8),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: theme.colorScheme.primaryContainer.withOpacity(0.3),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: theme.colorScheme.primary.withOpacity(0.3),
                ),
              ),
              child: Row(
                children: [
                  Icon(
                    LucideIcons.lightbulb,
                    color: theme.colorScheme.primary,
                    size: 20,
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      'Tip: When viewing a specific shared list, quick-add will prioritize that list over your default preferences.',
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: theme.colorScheme.primary,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}