# Technical Context: Task OrganAIzer

*Source: docs/technical-architecture.md (v1.0, March 25, 2025)*

## Technologies Used

*   **Frontend (Web):**
    *   Next.js (React framework)
    *   React Context API for state management
    *   Tailwind CSS with shadcn/ui components
    *   Axios for API calls
    *   React Hook Form for forms
    *   Browser Geolocation API (via custom hooks)
    *   Firebase Cloud Messaging (FCM) for web push notifications
    *   Service workers for background notification handling
    *   VAPID key authentication for web push
    *   **Voice input planned, not yet implemented**
*   **Mobile App (Flutter):**
    *   Flutter framework (SDK ^3.7.2)
    *   Riverpod (`flutter_riverpod` ^2.6.1, `riverpod_annotation` ^2.6.1) for state management
    *   Dio (^5.8.0+1) for HTTP client with V2 API support
    *   Go Router (^13.2.5) for navigation
    *   Flutter Secure Storage (^9.2.4) for secure token storage
    *   Mockito and HTTP mock adapter (`http_mock_adapter` ^0.6.1) for V2 endpoint testing
    *   Platform detection for dynamic API configuration
    *   V2 API integration with standardized response handling
    *   Comprehensive test coverage for V2 endpoints
    *   Lucide Icons (^0.257.0) for UI elements (**Used throughout the app including GroceryListItem**)
    *   Google Fonts (^6.1.0) for typography
    *   Permission Handler (^11.4.0) for permission management (**Microphone, Location**)
    *   Geolocator (^11.0.0) for location services (**Used by GeolocationService**)
    *   Speech to Text (^7.0.0) for voice recognition (**Used in RecordingModal**)
    *   Intl (^0.19.0) for internationalization and formatting (**Used for date formatting in GroceriesScreen**)
    *   Gap (^3.0.1) for layout spacing
    *   **Location Features:**
        *   `flutter_map` (^6.1.0) for interactive maps
        *   `latlong2` (^0.9.1) for geographic coordinates
        *   `flutter_typeahead` (^5.0.0) for location search with typeahead
        *   Mapbox Geocoding API integration for address search and reverse geocoding
        *   Custom `MapWithSearch` component with central marker and search integration
        *   Web-like location search experience optimized for mobile touch interactions
        *   Natural gesture handling (pan, pinch to zoom) for map navigation
        *   Current location tracking with visual indicator
        *   Debounced reverse geocoding during map movement
        *   Automatic address display with updates during map panning
        *   Simplified location addition workflow with "Add Here" button
        *   Pre-filling of address in location dialog from search or reverse geocoding
        *   Enhanced visual feedback for currently selected location
        *   Improved error handling and loading states for geocoding operations
    *   **Environment-Aware API Configuration:**
        *   Environment detection using `kReleaseMode` from Flutter foundation
        *   Dynamic API URL configuration based on build mode (debug vs release)
        *   Server configuration persistence using SharedPreferences
        *   Production URL (`https://flashtasks-ai.onrender.com`) for release builds
        *   Development URLs (`********:3001` for Android emulator, `localhost:3001` for others)
        *   Provider-based URL management with invalidation on changes
        *   Server configuration screen for development environments
    *   **Permission Management:**
        *   Platform-specific permission declarations (AndroidManifest.xml, Info.plist)
        *   Centralized `PermissionsService` for permission handling
        *   User-friendly `PermissionRequestDialog` for permission explanations
        *   Automatic permission requests on app startup
        *   Support for graceful permission denial handling
*   **Backend:**
    *   Node.js with Express
    *   Mongoose ODM for MongoDB
    *   **Standardized API Response Format (Completed May 2025):**
        *   Consistent structure: `{ success: true/false, data: [...], pagination: {...}, error: {...} }`
        *   Standardized error responses: `{ success: false, error: { message: '...', code: '...' } }`
        *   Consistent pagination format: `{ currentPage, totalPages, pageSize, totalItems }`
        *   Error codes for better client-side error handling:
            *   `AUTHENTICATION_REQUIRED`: User is not authenticated
            *   `RESOURCE_NOT_FOUND`: Requested resource doesn't exist
            *   `VALIDATION_ERROR`: Input validation failed
            *   `SERVER_ERROR`: Internal server error
            *   `FORBIDDEN_OPERATION`: User doesn't have permission
            *   `DUPLICATE_RESOURCE`: Resource already exists
            *   `INVALID_ID_FORMAT`: Invalid MongoDB ID format
        *   Backward compatibility with legacy response formats
        *   Implemented across all controllers (V1 and V2)
        *   Enhanced client-side parsing with predictable response structure
    *   V2 API implementation with standardized responses
    *   Mobile-optimized endpoints with reduced payload size
    *   Standardized pagination helper for consistent list responses
    *   Enhanced error handler for consistent error responses
    *   Authentication middleware applied at route group level
    *   JWT authentication via HTTP-only cookies (web) and Bearer tokens (mobile)
    *   Firebase Admin SDK for push notifications
    *   Cross-platform notification service with standardized payload structure
    *   Dual configuration support for Firebase credentials (JSON string or file path)
    *   AI integration via Google Gemini API
    *   Web search via Brave Search API
    *   Location suggestion service
    *   Node-cron for scheduled tasks
    *   Performance monitoring middleware
    *   Database query monitoring
    *   In-memory caching for read-heavy routes
    *   PM2 for application monitoring
    *   Notification dispatching capability for task reminders
*   **Database:**
    *   MongoDB (primary, with 2dsphere geospatial indexing)
*   **AI/ML:**
    *   External APIs (Gemini, Brave Search)
    *   No local ML models or Python services currently
    *   Structured AI prompts with contextual information
    *   JSON response parsing and validation
*   **Deployment:**
    *   Render.com for backend deployment
    *   Vercel for frontend deployment
    *   Custom build script to handle TypeScript compilation
    *   Configuration via render.yaml (both root and backend directory)
    *   Root-level .buildpacks file for Render detection
    *   Local/dev environment
    *   Cloud deployment, container orchestration, serverless planned for future
    *   **Mobile App Distribution:**
        *   Firebase App Distribution for testing
        *   Direct Firebase CLI approach (not Flutter plugin) for more reliable distribution
        *   Service account authentication with proper IAM roles for automation
        *   Distribution scripts for Android (distribute.ps1/distribute.sh)
        *   Support for both Android and iOS distribution
        *   Platform-specific permission handling
*   **Location Services:**
    *   Browser Geolocation API
    *   MongoDB geospatial queries
    *   Haversine formula for distance
    *   Multi-factor weighted scoring algorithm
    *   Integration with AI task creation flow
*   **Push Notification System:**
    *   Firebase Cloud Messaging (FCM) for web notifications
    *   Service worker registration and management
    *   Background message handling
    *   Notification click handling with custom actions
    *   Token registration and management API
    *   Permission handling and user preferences
    *   VAPID key authentication for Web Push API
    *   Custom testing tools for notification debugging
*   **Default Grocery List Preferences System (Implemented 2025-05-27):**
    *   V2 Settings API with grocery preferences endpoints (`/api/settings/grocery-preferences`)
    *   Context-aware quick-add functionality that respects user's default list preferences
    *   Smart context building logic prioritizing current list over default preferences
    *   Frontend preferences UI with shared list selection and quick access from grocery page
    *   User preferences integration with existing settings system
    *   API endpoint standardization across all settings routes
    *   Enhanced QuickInput and TopNavigationBar components with preference awareness
    *   GroceryProvider integration in settings layout for context access
    *   Comprehensive validation for preference fields and shared list access
    *   Real-time preference updates affecting AI quick-add behavior
*   **Grocery List Collaboration (Implemented 2025-05-27):**
    *   Extended existing GroceryList model with collaboration features
    *   Role-based access control (Owner/Editor/Viewer) with proper validation
    *   Email-based user identification for collaborator invitations
    *   V2 API endpoints for all collaboration operations
    *   Real-time state management via React Context API
    *   Email notification service integration for invitations
    *   Comprehensive error handling with specific error codes
    *   Frontend components: GroceryCollaborationPanel, SharedListsBrowser, GroceryListHeader
    *   Backend validation middleware for all collaboration routes
    *   Database schema extensions for collaborators and share settings
*   **Mobile Shared Groceries (Implemented 2024-12-30):**
    *   Complete Flutter implementation of shared groceries functionality
    *   Enhanced data models with collaboration tracking fields (addedBy, checkedBy, lastModifiedBy, checkedAt)
    *   Popup-based UI architecture using showModalBottomSheet with DraggableScrollableSheet
    *   Provider state management with Riverpod for collaboration state
    *   Visual collaboration indicators in grocery items when viewing shared lists
    *   Integration with existing v2 API endpoints (no backend changes required)
    *   Comprehensive test coverage with 8 passing tests for all collaboration models
    *   Key components: CollaborationPanel, SharedListSelector, GroceryQuickAddWidget
    *   Enhanced GroceryProvider with shared list switching and invitation management
    *   Backend support verification: all collaboration endpoints fully functional
*   **Other:**
    *   Email service, advanced AI/ML, Android-specific tech are **future scope**

## Development Setup

*   **Prerequisites:** Node.js, Python, Docker (recommended for local env), Android SDK/Studio (for Android dev), potentially Kotlin, Java. (Inferred from Tech Stack)
*   **Getting Started:** Clone repository, use Docker for local environment setup, install dependencies (npm/yarn/pip), configure environment variables, run backend services and frontend apps. (TA 9.1.1)
*   **Environment Variables:** Specific variables needed for database connections, API keys (Brave Search, FCM, Cloud Provider), JWT secrets, etc. Refer to `.env.example` files in `backend/` and potentially `frontend/`. (Inferred, TA 9.1.1)

## Technical Constraints

*   **Platforms:** Initial release targets Android and Web only. (PRD 1.2)
*   **Performance:** Specific targets for response times (<1s create, <2s AI processing, <1s search, <3s startup). (PRD 5.1)
*   **Security:** Initial focus on secure auth, data encryption. Advanced features later. (PRD 5.2)
*   **Reliability:** Basic offline functionality required for mobile. (PRD 5.3)
*   **Scalability:** Architecture must support user growth. (PRD 5.4)
*   **Browser Compatibility:** Location suggestion requires browser support for Geolocation API and Permissions API, with fallbacks for unsupported browsers.
*   **Deployment Constraints:** Backend TypeScript code must compile even with type errors using custom build script for Render deployment.
*   **Coordinate Format Handling:** Frontend map components require GeoJSON format coordinates (`{ type: 'Point'; coordinates: [number, number] }`), while the API supports both GeoJSON and direct formats (`{ latitude: number; longitude: number }`). Type conversions with proper type guards must be implemented to ensure TypeScript compatibility.
*   **Mobile Permissions:** The mobile app requires user permissions for:
    *   **Location Access:** For location-based tasks and reminders
    *   **Microphone Access:** For voice notes and commands
    *   Apps must handle permission denied states gracefully with proper user feedback
    *   Firebase App Distribution must be set up with proper permission declarations for testing

## Dependencies

*   **Key Backend Dependencies (Node.js):** Express/NestJS, Mongoose/Sequelize, JWT library (e.g., jsonwebtoken), potentially Swagger UI, Axios (for inter-service calls), FCM library, AWS/GCP SDK, Anthropic API (configurable model selection). (Inferred from TA)
*   **Key Backend DevDependencies:** TypeScript, @types/node, @types/express, @types/jest (for testing), @types/node-cron, @types/date-fns, ts-node, ts-node-dev.
*   **Key Backend Dependencies (Python/AI):** FastAPI/Flask, spaCy/NLTK/transformers, TensorFlow/PyTorch, Whisper library, requests (for Brave API). (Inferred from TA)
*   **Key Frontend Dependencies (Web):** React, Redux/Context, Styled Components/Tailwind, Axios, React Hook Form, React-Map-GL. (TA 2.2.1)
*   **Key Frontend Dependencies (Android):** Kotlin stdlib, Jetpack Compose libraries, Retrofit, OkHttp, Room, Google Play Services (Location). (TA 2.1.1)
*   **Location Dependencies:**
    *   Browser Geolocation API
    *   Permissions API
    *   MongoDB 2dsphere geospatial indexing
    *   Custom hooks (`useGeolocation.ts`)
    *   `geolib` for Haversine calculations (or custom implementation)
*   **AI Dependencies:**
    *   Google Gemini API client library
    *   Brave Search API (via MCP server)
    *   JSON validation utilities
    *   Structured prompt templates
*   **Backend API Structure:**
    *   **Consolidated API (September 2025):** Unified API structure for both web and mobile clients
    *   **Standardized Response Format (Completed May 2025):**
        *   Consistent structure: `{ success: true/false, data: [...], pagination: {...}, error: {...} }`
        *   Standardized error responses: `{ success: false, error: { message: '...', code: '...' } }`
        *   Consistent pagination format: `{ currentPage, totalPages, pageSize, totalItems }`
        *   Error codes for better client-side error handling:
            *   `AUTHENTICATION_REQUIRED`: User is not authenticated
            *   `RESOURCE_NOT_FOUND`: Requested resource doesn't exist
            *   `VALIDATION_ERROR`: Input validation failed
            *   `SERVER_ERROR`: Internal server error
            *   `FORBIDDEN_OPERATION`: User doesn't have permission
            *   `DUPLICATE_RESOURCE`: Resource already exists
            *   `INVALID_ID_FORMAT`: Invalid MongoDB ID format
        *   Applied to both V1 and V2 endpoints for consistency
        *   Backward compatibility with legacy response formats
        *   Implemented across all controllers (V1 and V2)
        *   Enhanced client-side parsing with predictable response structure
    *   Full data models returned for all clients with proper population of related documents
    *   Standardized pagination helper for consistent list responses
    *   Enhanced error handler for consistent error responses
    *   Authentication middleware applied at route group level
    *   **Legacy Structure (Before September 2025):**
        *   V1 API: Original endpoints used by the web frontend
        *   V2 API: Mobile-optimized endpoints with reduced payload size
*   **Push Notification Dependencies:**
    *   firebase/app: Firebase app core functionality
    *   firebase/messaging: Firebase Cloud Messaging client
    *   Web Push API (browser native)
    *   Service Worker API (browser native)
    *   Notification API (browser native)
    *   Custom notification-service.ts for token management
    *   firebase-messaging-sw.js service worker implementation
    *   Environment variables for Firebase configuration

## Deployment Configuration

### Vercel Setup (Frontend)

1. **Configuration**
   - Connected to GitHub repository for automatic deployments
   - Uses standard Next.js build process
   - Requires proper TypeScript type declarations for all dependencies
   - Uses custom build script (`build-vercel.js`) to handle dependency conflicts
   - Configured with `vercel.json` to bypass frozen lockfile requirements

2. **Common Issues and Solutions**
   - Missing type declarations causing TypeScript errors (`@types/js-cookie`, `@types/date-fns`, `@types/node-cron`)
   - Solution: Install required type declarations as dev dependencies
   - **Update (April 2025):** The `@types/date-fns` issue was resolved by creating a custom type declaration file (`frontend/types/date-fns.d.ts`) as `date-fns` v3+ includes its own types, but the build system still required explicit definitions for the functions used.
   - Windows-specific build errors related to corrupted `.next` directory
   - Solution: Remove `.next` directory and reinstall dependencies before building
   - Dependency conflicts (React 19 vs React day-picker requirements)
   - Solution: Use `--legacy-peer-deps` flag during builds

3. **Custom Build Script**
   - **Purpose**: Handle cross-platform builds and dependency conflicts
   - **Functions**:
     - Auto-installs required type declarations using `--legacy-peer-deps`
     - Cleans `.next` directory before build (platform-aware for Windows vs Linux)
     - Creates optimized TypeScript config for builds
     - Uses `--legacy-peer-deps` for package installations and builds
   - **Benefits**:
     - Works consistently across different environments
     - Handles dependency conflicts without requiring package downgrades
     - Provides better error recovery during build process
     - Maintains cleaner build artifacts

4. **Vercel Configuration**
   - Uses `vercel.json` to override default build behavior
   - Disables frozen lockfile requirement with `--no-frozen-lockfile`
   - Avoids lockfile conflicts after adding new dependencies
   - Allows builds to succeed even with mismatched package-lock/package.json

### Render.com Setup (Backend)

1. **Configuration Files**
   - **Repository Root**:
     - `render.yaml`: Defines service configuration with rootDir pointing to backend
     - `.buildpacks`: Tells Render which directories contain which types of apps
   - **Backend Directory**:
     - `render.yaml`: Backend-specific configuration
     - `.render-buildpacks.json`: Specifies build dependencies and commands
     - `render-build.sh`: Custom build script for handling PATH issues and file copying

2. **Build Process**
   - TypeScript compilation with `tsc`
   - Special handling for non-TypeScript files (YAML, etc.)
   - Custom `render-build.sh` script for Render deployments that:
     - Installs dependencies with `yarn`
     - Installs copyfiles both globally and locally
     - Explicitly adds yarn bin directory to PATH
     - Runs TypeScript compilation
     - Uses npx to run copyfiles for YAML prompt files
   - Package.json scripts:
     - `build`: Standard build command for local development
     - `build:prod`: Production build command
     - `render-build`: Specialized script for Render deployment
     - `copy-files`: Uses npx to reliably execute copyfiles

3. **Non-TypeScript File Handling**
   - TypeScript compiler does not copy non-TS files to build output
   - YAML files (containing AI prompts) require special handling
   - The `copyfiles` package is used with the `-u 1` flag to maintain directory structure
   - Pattern `src/**/*.yaml` ensures all YAML files in the src directory are copied
   - PATH resolution can be tricky in CI environments, so npx is used for reliability

4. **Common Issues and Solutions**
   - "Prompt not found" errors indicate YAML files weren't copied to the build
   - Binary path resolution issues in CI environments
   - Solution: Use npx with explicit PATH handling in build scripts
   - Documentation in backend/BUILD.md explains the process and troubleshooting

5. **Environment Configuration**
   ```yaml
   # Root render.yaml example
   services:
     - type: web
       name: task-organizer-backend
       env: node
       rootDir: backend
       buildCommand: npm install && npm run build:prod
       startCommand: npm start
       envVars:
         - key: NODE_ENV
           value: production
   ```

6. **TypeScript Configuration**
   - Modified `tsconfig.json` with relaxed type checking:
     - `strictNullChecks: false`
     - `noImplicitAny: false`
     - `noImplicitReturns: false`
   - These settings allow compilation to succeed despite type errors

7. **Common Deployment Issues**
   - Missing type definitions (like `@types/jest`, `@types/node-cron`, `@types/date-fns`)
   - TypeScript errors in route handlers
   - Null checking errors in controller code
   - Function return type mismatches
   - Missing runtime dependencies
   - Incorrect build commands in Render configuration

### Local Development vs. Deployment

1. **Local Development**
   - Uses standard TypeScript build: `npm run build`
   - Stricter type checking encouraged for development
   - TypeScript errors should be addressed

2. **Production Deployment**
   - Uses custom build script: `npm run build:prod`
   - Relaxed type checking to ensure builds succeed
   - More tolerant of TypeScript errors
   - Auto-installs missing dependencies

3. **Long-term Considerations**
   - Address TypeScript errors in the codebase
   - Improve typing in route handlers and controllers
   - Add explicit type declarations for all dependencies
   - Eventually remove the need for the custom build script
   - Consider using Docker for more consistent deployment

## Task Reordering Implementation

### Libraries and Dependencies
1. Drag and Drop
   - @dnd-kit/core: Core drag and drop functionality
   - @dnd-kit/sortable: Sortable list implementation
   - @dnd-kit/modifiers: Drag movement modifiers

2. State Management
   - React useState and useEffect for local state
   - Custom TaskOrderService for order management
   - Callback system for state synchronization

### Technical Constraints
1. MongoDB Setup
   - Running in standalone mode
   - No transaction support available
   - Need alternative approaches for data consistency

2. Performance Limitations
   - Touch device latency considerations
   - UI freezing during drag operations
   - State update synchronization delays

### Development Setup
1. Frontend
   ```typescript
   // Task order service initialization
   const taskOrderService = new TaskOrderService()

   // Component setup
   const DraggableTaskList = ({
     tasks,
     contextId = 'global',
     onReorder
   }) => {
     // Implementation
   }
   ```

2. Backend
   ```typescript
   // Task model
   interface Task {
     _id: string
     orderData: {
       position: number
       contextId: string
       lastUpdated: Date
     }
   }

   // API endpoints
   POST /api/tasks/reorder
   GET /api/tasks/ordered/:contextId
   ```

### Tool Usage Patterns
1. Drag and Drop Configuration
   ```typescript
   const sensors = useSensors(
     useSensor(PointerSensor, {
       activationConstraint: {
         distance: 8,
         delay: 0
       }
     })
   )
   ```

2. Order Management
   ```typescript
   // Position calculation
   const newPosition = calculatePosition(prevPos, nextPos)

   // Order update
   await taskOrderService.queueReorder(taskId, newPosition)
   ```

### Error Handling
1. Retry Mechanism
   ```typescript
   // Exponential backoff
   const delay = Math.min(
     initialDelay * Math.pow(2, retryCount),
     maxDelay
   )
   ```

2. Error Recovery
   ```typescript
   try {
     await executeReorder()
   } catch (error) {
     if (canRetry) {
       await retry()
     } else {
       revertChanges()
     }
   }
   ```

### Performance Optimization
1. Drag Sensor Settings
   - Mouse: No delay, minimal distance
   - Touch: 100ms delay, 5px tolerance

2. State Updates
   - Optimistic UI updates
   - Queued backend operations
   - Batched state changes

3. Performance Monitoring
   - Server-side request tracking with performance middleware
   - Database query monitoring with Mongoose debug mode
   - Client-side API call tracking in BaseService
   - PM2 metrics collection for CPU and memory usage
   - In-memory caching for frequently accessed data

### Development Guidelines
1. Code Organization
   - Separate service layer for order management
   - Component-specific drag handlers
   - Shared utility functions

2. Testing Considerations
   - Unit tests for position calculation
   - Integration tests for order persistence
   - Performance testing for large lists

3. Debugging Tools
   - Console logging for order operations
   - Error tracking for failed updates
   - Performance monitoring

### Known Limitations
1. Technical
   - No transaction support in MongoDB standalone
   - Performance issues with large lists
   - Touch device latency

2. Functional
   - No multi-item drag support
   - Limited undo/redo capabilities
   - No offline support

### Future Considerations
1. Performance
   - Implement virtual scrolling
   - Optimize drag preview rendering
   - Add batch update support

2. Features
   - Multi-item selection
   - Cross-list dragging
   - Keyboard navigation

3. Infrastructure
   - Consider MongoDB replica set
   - Add proper monitoring
   - Implement caching

## Location Suggestion System Technical Implementation

### Core Components

1. **Backend Service**
   - `locationSuggestionService.ts`: Core service implementing the multi-factor scoring algorithm
   - Location model with geospatial indexing support
   - API endpoint (`/api/locations/suggest`) accepting task and user parameters

2. **Frontend Integration**
   - `useGeolocation.ts`: Custom hook for browser geolocation access
   - `task-location-service.ts`: Extended service with suggestion support
   - UI components with visual indicators for suggestion quality

### Implementation Details

1. **Position Tracking**
   ```typescript
   // Geolocation hook
   function useGeolocation() {
     // State variables
     const [position, setPosition] = useState(null)
     const [permissionStatus, setPermissionStatus] = useState('prompt')

     // Watch position with error handling
     useEffect(() => {
       if (!navigator.geolocation) return

       const watchId = navigator.geolocation.watchPosition(
         pos => {
           setPosition({
             latitude: pos.coords.latitude,
             longitude: pos.coords.longitude
           })
         },
         err => {
           console.error('Geolocation error:', err)
           setPermissionStatus(
             err.code === 1 ? 'denied' : 'error'
           )
         },
         {
           enableHighAccuracy: true,
           maximumAge: 30000,
           timeout: 10000
         }
       )

       return () => navigator.geolocation.clearWatch(watchId)
     }, [])

     return { position, permissionStatus, requestPermission }
   }
   ```

2. **Scoring Algorithm**
   ```typescript
   // Backend scoring implementation
   async function suggestLocations(params) {
     const { userId, currentLat, currentLon, taskTitle, taskDescription, categoryIds } = params

     // Get user locations
     const locations = await Location.find({ userId })

     // Score and rank locations
     const scored = locations.map(location => {
       // Proximity score (if coordinates available)
       let proximityScore = 0
       if (currentLat && currentLon) {
         const distance = calculateDistance(
           currentLat, currentLon,
           location.coordinates.lat, location.coordinates.lon
         )
         // Normalize based on distance and location radius
         proximityScore = calculateProximityScore(distance, location.radius || 500)
       }

       // Keyword score from task text
       const taskText = `${taskTitle || ''} ${taskDescription || ''}`
       const keywordScore = calculateKeywordScore(taskText, location.name)

       // Category mapping score
       const categoryScore = await calculateCategoryScore(categoryIds, location._id, userId)

       // Final weighted score
       const finalScore = (
         (proximityScore * 0.4) +
         (keywordScore * 0.4) +
         (categoryScore * 0.2)
       )

       return {
         location,
         score: finalScore,
         factors: {
           proximity: proximityScore,
           keyword: keywordScore,
           category: categoryScore
         }
       }
     })

     // Sort by score and return
     return scored.sort((a, b) => b.score - a.score)
   }
   ```

3. **API Integration**
   ```typescript
   // Controller endpoint
   router.post('/api/locations/suggest', async (req, res) => {
     try {
       const { currentLat, currentLon, taskTitle, taskDescription, categoryIds } = req.body
       const userId = req.user.id

       const suggestions = await locationSuggestionService.suggestLocations({
         userId,
         currentLat,
         currentLon,
         taskTitle,
         taskDescription,
         categoryIds
       })

       res.json({ suggestions })
     } catch (error) {
       console.error('Location suggestion error:', error)
       res.status(500).json({ error: 'Failed to get location suggestions' })
     }
   })
   ```

## Performance Monitoring Implementation

### Core Components

1. **Server-Side Monitoring**
   - `performanceMiddleware.ts`: Middleware that tracks request durations and identifies slow requests
   - Integration with secure logger for performance data logging
   - Skip logic for static assets and health checks

2. **Database Query Monitoring**
   - `dbMonitor.ts`: Utility for monitoring database queries
   - Mongoose debug mode in development
   - Query performance tracking with slow query identification
   - MongoDB plugin for automatic query monitoring

3. **Client-Side Monitoring**
   - BaseService performance tracking for all API calls
   - Duration measurement for service operations
   - Detailed logging for performance metrics

4. **Caching**
   - `cacheMiddleware.ts`: Simple in-memory caching for frequently accessed data
   - Cache control for GET requests
   - Configurable cache duration
   - Applied to read-heavy routes

5. **System Monitoring**
   - PM2 configuration for application monitoring
   - Metrics collection for CPU and memory usage
   - Log aggregation and formatting
   - Dashboard for real-time monitoring

### Implementation Details

1. **Performance Middleware**
   ```typescript
   export const performanceMiddleware = (req: Request, res: Response, next: NextFunction): void => {
     // Skip monitoring for static assets and health checks
     if (req.path.startsWith('/static') || req.path === '/health') {
       return next();
     }

     // Record start time
     const start = Date.now();

     // Listen for the 'finish' event to calculate duration
     res.on('finish', () => {
       const duration = Date.now() - start;

       // Log performance data
       secureLogger.log(`[PERFORMANCE] ${req.method} ${req.originalUrl} ${res.statusCode} ${duration}ms`);

       // Log slow requests separately (over 500ms)
       if (duration > 500) {
         secureLogger.warn(`[SLOW REQUEST] ${req.method} ${req.originalUrl} took ${duration}ms`);
       }
     });

     next();
   };
   ```

2. **Database Monitoring**
   ```typescript
   export const monitorQuery = (query: mongoose.Query<any, any>): mongoose.Query<any, any> => {
     const start = Date.now();
     const originalExec = query.exec;

     query.exec = function() {
       return originalExec.apply(this).then((result: any) => {
         const duration = Date.now() - start;

         // Log all queries
         secureLogger.log(`[DB QUERY] ${this.model.modelName}.${this.op} completed in ${duration}ms`);

         // Log slow queries separately (over 100ms)
         if (duration > 100) {
           secureLogger.warn(`[SLOW QUERY] ${this.model.modelName}.${this.op} took ${duration}ms`);
         }

         return result;
       });
     };

     return query;
   };
   ```

3. **Caching Implementation**
   ```typescript
   export const cacheMiddleware = (duration: number = DEFAULT_CACHE_EXPIRATION) => {
     return (req: Request, res: Response, next: NextFunction): void => {
       // Only cache GET requests
       if (req.method !== 'GET') {
         return next();
       }

       // Create a cache key from the request URL
       const key = req.originalUrl;

       // Check if we have a cached response
       const cachedResponse = cache.get(key);
       if (cachedResponse && now - cachedResponse.timestamp < duration) {
         secureLogger.log(`[CACHE] Cache hit: ${key}`);
         res.json(cachedResponse.data);
         return;
       }

       // Cache miss, continue with the request
       secureLogger.log(`[CACHE] Cache miss: ${key}`);

       // Override res.json to cache the response
       const originalJson = res.json;
       res.json = function(data: any): Response {
         cache.set(key, { data, timestamp: Date.now() });
         secureLogger.log(`[CACHE] Cached: ${key}`);
         return originalJson.call(this, data);
       };

       next();
     };
   };
   ```

## QuickAdd Flow Technical Implementation

### Core Components

1. **AI Controller Enhancement**
   - `aiController.js`: Updated with location suggestion integration
   - New multi-step location processing logic
   - Improved error handling and validation

2. **Integration Points**
   - Direct connection to `locationSuggestionService`
   - Support for coordinates in QuickAdd requests
   - Respect for user confirmation settings

### Implementation Details

1. **AI Controller Update**
   ```typescript
   // Enhanced process method
   async function processQuickAdd(input, userId, coordinates = null) {
     try {
       // Get user data for context
       const user = await User.findById(userId)
       const categories = await Category.find({ userId })
       const locations = await Location.find({ userId })

       // Build context-aware prompt
       const prompt = buildPrompt(input, {
         currentTime: new Date().toISOString(),
         coordinates,
         categories: categories.map(c => c.name),
         locations: locations.map(l => l.name)
       })

       // Process with AI
       const aiResponse = await geminiService.generateContent(prompt)

       // Parse and validate response
       const parsedResponse = validateAIResponse(aiResponse)

       // Process location
       const locationData = await processLocation({
         extractedLocation: parsedResponse.location,
         taskText: `${parsedResponse.title} ${parsedResponse.description || ''}`,
         categories: parsedResponse.categories,
         userId,
         coordinates
       })

       // Create task with location
       const task = await createTask({
         ...parsedResponse,
         locationId: locationData.locationId,
         userId
       })

       return {
         task,
         locationSuggestion: locationData.suggestion
       }
     } catch (error) {
       console.error('QuickAdd processing error:', error)
       throw new Error(`Failed to process quick add: ${error.message}`)
     }
   }
   ```

2. **Location Processing**
   ```typescript
   // Location processing function
   async function processLocation({ extractedLocation, taskText, categories, userId, coordinates }) {
     // Step 1: Try to find location by name if explicit
     if (extractedLocation) {
       const location = await Location.findOne({
         userId,
         name: { $regex: new RegExp(extractedLocation, 'i') }
       })

       if (location) {
         return {
           locationId: location._id,
           suggestion: null
         }
       }
     }

     // Step 2: Call suggestion service if no explicit location
     // or if explicit location not found
     const categoryIds = await getCategoryIds(categories, userId)

     if (coordinates || categoryIds.length > 0 || taskText) {
       const suggestions = await locationSuggestionService.suggestLocations({
         userId,
         currentLat: coordinates?.latitude,
         currentLon: coordinates?.longitude,
         taskTitle: taskText,
         categoryIds
       })

       if (suggestions?.length > 0) {
         // Get user settings to check auto-confirmation
         const userSettings = await UserSettings.findOne({ userId })

         return {
           locationId: userSettings?.autoMapWithoutConfirmation
             ? suggestions[0].location._id
             : null,
           suggestion: suggestions[0]
         }
       }
     }

     // No location found
     return {
       locationId: null,
       suggestion: null
     }
   }
   ```

3. **Frontend Integration**
   ```typescript
   // QuickAdd component
   function QuickAdd() {
     const { user } = useAuth()
     const { position } = useGeolocation()
     const [input, setInput] = useState('')
     const [loading, setLoading] = useState(false)
     const [suggestion, setSuggestion] = useState(null)

     const handleSubmit = async (e) => {
       e.preventDefault()
       setLoading(true)

       try {
         const response = await axios.post('/api/ai/quickadd', {
           input,
           coordinates: position ? {
             latitude: position.latitude,
             longitude: position.longitude
           } : null
         })

         // Handle response with potential location suggestion
         if (response.data.locationSuggestion) {
           setSuggestion(response.data.locationSuggestion)
         } else {
           // Auto-redirect or show success
           router.push('/dashboard')
         }
       } catch (error) {
         console.error('QuickAdd error:', error)
         setError('Failed to process task')
       } finally {
         setLoading(false)
       }
     }

     // Render form and suggestion UI
   }
   ```

## Authentication Technologies

### Core Dependencies
- **jsonwebtoken**: JWT generation and validation
- **bcrypt**: Password hashing
- **express-rate-limit**: Rate limiting middleware
- **helmet**: Security headers
- **cors**: CORS configuration
- **csurf**: CSRF protection
- **redis**: Token storage and rate limiting

### Security Configuration
```typescript
// JWT Configuration
const jwtConfig = {
  algorithm: 'HS256',
  accessTokenExpiry: '15m',
  refreshTokenExpiry: '7d'
};

// Rate Limiting
const rateLimitConfig = {
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100 // limit each IP to 100 requests per windowMs
};

// CORS Settings
const corsConfig = {
  origin: process.env.ALLOWED_ORIGINS.split(','),
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE']
};
```

### Development Setup
1. Redis server for token management
2. Environment variables for secrets
3. HTTPS in production
4. Postman/Insomnia collections for testing

### Technical Constraints
- Minimum password requirements
- Token expiration policies
- Rate limiting thresholds
- CORS origin restrictions
- Cookie security settings

### Monitoring Tools
- Authentication metrics dashboard
- Security event logging
- Performance monitoring
- Error tracking system

## Backend

### AI Components

#### AI Integration Architecture
The application integrates various AI capabilities using a service-based approach:

1. **AI Provider Service**
   - Acts as abstraction layer for AI model access
   - Supports pluggable AI providers (Google Gemini, OpenAI, etc.)
   - Handles prompt formatting and response parsing
   - Manages API quotas and fallbacks

2. **Prompt Service**
   - Maintains prompt templates in YAML files
   - Provides helper methods for prompt construction
   - Supports variable substitution in prompts
   - Centralizes prompt management across the application

3. **Key AI Controller Functions**
   - `processQuickAdd`: Handles natural language input for task and grocery creation. Evolved to support advanced multi-task parsing, per-task attribute extraction (title, content, categories, location, deadline, time, priority, reminders), and implicit project title suggestion.
   - `analyzeContent`: Suggests categories based on task content
   - `suggestTaskPriority`: Recommends priority level changes
   - `askChatbot`: Provides conversational assistance

4. **AI Data Processing & Error Handling**
   - **Date Parsing**:
     - Function: `parseDateString` in `aiController.ts`
     - Handles various date formats including standard ISO, relative dates, and common formats (MM/DD/YYYY, DD/MM/YYYY)
     - Includes structured logging for parsing failures
     - Stores original unparsed strings in task metadata when parsing fails
   - **JSON Parsing**:
     - Uses `safeJsonParse` utility to handle malformed AI outputs
     - Validates expected structure and types after parsing
   - **UI Feedback**:
     - Visual indicators for AI-related issues (e.g., date parsing failures)
     - Tooltips with original AI outputs for transparency
     - Warning messages in edit forms for user intervention

5. **AI Response Schema**
   - JSON structure for `processQuickAdd` (tasks): `{ "suggestedProjectTitle": "string_or_null", "tasks": [ { "intent": "task", "title": "string", "content": "string_or_null", "categoryNames": [], "locationName": "string_or_null", "deadline": "YYYY-MM-DD_or_null", "time": "HH:MM_or_null", "priority": "string_or_null", "reminders": [] } ] }`
   - JSON structure for `processQuickAdd` (groceries): `{ "suggestedProjectTitle": null, "tasks": [ { "intent": "grocery", "items": [ { "name": "string", "quantity": "string_or_null" } ] } ] }`
   - JSON structure for chatbot: `{ answer, needsWebSearch, searchQuery }`
   - JSON structure for insights: Various types based on insight category

*This document details the technologies, setup, and constraints for the project.*

## Build and Deployment

### Package Management
- Using Yarn as the primary package manager
- No mixing of package managers (npm/yarn) to avoid dependency conflicts
- Package-lock.json has been removed to prevent inconsistencies

### TypeScript Configuration
- Production builds exclude test files and test-related types
- Test files excluded patterns:
  - `src/tests/**/*`
  - `**/*.test.ts`
- Types configuration in tsconfig.json:
  ```json
  {
    "types": ["node"],
    "typeRoots": ["./node_modules/@types", "./src/types"]
  }
  ```

### Render Deployment
- Builds run in production mode
- Build command: `yarn && yarn build`
- Start command: `yarn start`
- Environment: Node.js
- Dependencies:
  - Production dependencies in `dependencies`
  - Test/development tools in `devDependencies`
  - Frontend-specific packages kept separate from backend

### Development Setup
- TypeScript for type safety
- Jest for testing (development only)
- ESLint for code quality
- Prettier for code formatting

## Dependencies Management
### Production Dependencies
- Core backend packages (express, mongoose, etc.)
- Type definitions needed for compilation
- Runtime dependencies

### Development Dependencies
- Testing frameworks (Jest, Supertest)
- Development tools (ts-node-dev, ESLint)
- Test-specific type definitions

## Important Technical Decisions
1. Separation of test and production code in build process
2. Strict package manager standardization (Yarn)
3. Clear distinction between frontend and backend dependencies

## June 2024
- Kotlin Gradle plugin: 2.1.20
- Android Gradle Plugin: 8.7.0
- These must be kept in sync with Firebase plugin requirements for successful builds.
