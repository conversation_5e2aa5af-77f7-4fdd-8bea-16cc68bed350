# System Patterns & Learnings

*This file captures recurring patterns, architectural decisions, and lessons learned during development.*

---

## Default List Preferences System - Implemented 2025-05-27

**Problem:**
- Users needed a way to set default grocery list preferences for AI quick-add and suggestions
- Quick-add functionality was always adding items to personal lists regardless of user preferences
- Users wanted AI features to target their preferred shared lists automatically
- Settings needed to be easily accessible from both main settings and grocery page
- API endpoints were inconsistent after route restructuring

**Solution:**
1. **Backend V2 Settings API:**
   - Added grocery preferences endpoints to existing V2 settings controller:
     - `GET /api/settings/grocery-preferences` - Get current preferences
     - `PUT /api/settings/grocery-preferences` - Update preferences
   - Implemented comprehensive validation for preference fields
   - Added support for default list type (personal/shared) and specific shared list selection
   - Fixed route mounting from `/users/settings` to `/settings` for consistency

2. **Frontend Preferences System:**
   - Created dedicated `GroceryPreferencesSettings` component with intuitive UI
   - Implemented `grocery-preferences-service.ts` for API interactions
   - Updated existing `user-preferences-service.ts` to use correct endpoints
   - Added quick access settings button on grocery page for convenience
   - Enhanced settings layout with `GroceryProvider` for context access

3. **Context-Aware Quick Add:**
   - Enhanced `QuickInput` component with user preferences integration
   - Updated `TopNavigationBar` quick-add to respect default preferences
   - Implemented smart context building logic:
     - Priority 1: Current grocery list context (if on specific list)
     - Priority 2: User's default preferences (if configured)
     - Always includes user preferences for backend processing
   - Added `useUserPreferences()` hook integration for real-time preference access

4. **API Endpoint Standardization:**
   - Fixed all settings endpoints in `frontend/config.ts` from `/users/settings/*` to `/settings/*`
   - Corrected route mounting in backend from `/users/settings` to `/settings`
   - Updated keytag mappings and other settings endpoints for consistency
   - Ensured all settings APIs use the same path pattern

**Technical Patterns:**
```typescript
// Backend: Grocery preferences in V2 settings controller
async getGroceryPreferences(req: Request, res: Response) {
  const userSettings = await UserSettings.findOne({ userId: req.user.userId });

  if (!userSettings?.preferences?.groceryPreferences) {
    return res.json({
      success: true,
      data: {
        defaultListType: 'personal',
        defaultSharedListOwnerId: null,
        autoSwitchToDefault: false,
        showPersonalListInSidebar: true
      }
    });
  }

  return res.json({
    success: true,
    data: userSettings.preferences.groceryPreferences
  });
}

// Frontend: Context-aware quick add
const buildContext = () => {
  const context: any = {};

  // Priority 1: Current list context
  if (currentGroceryListOwnerId) {
    context.groceryListOwnerId = currentGroceryListOwnerId;
  }
  // Priority 2: Default preferences
  else if (groceryPreferences?.defaultListType === 'shared' &&
           groceryPreferences.defaultSharedListOwnerId) {
    context.groceryListOwnerId = groceryPreferences.defaultSharedListOwnerId;
  }

  // Always include preferences for backend
  if (groceryPreferences) {
    context.userPreferences = {
      defaultGroceryListType: groceryPreferences.defaultListType,
      defaultSharedListOwnerId: groceryPreferences.defaultSharedListOwnerId
    };
  }

  return context;
};
```

**Lessons Learned:**
- Context-aware behavior should prioritize current context over default preferences
- API endpoint consistency is crucial for maintainability and debugging
- Quick access to settings improves user experience significantly
- Smart defaults reduce configuration burden while providing flexibility
- User preferences should be included in AI context for backend processing
- Route restructuring requires careful verification of all affected endpoints

**Key Files:**
- `backend/src/controllers/v2/userSettingsController.ts` - Grocery preferences API
- `backend/src/routes/v2/settings.ts` - Settings routes with grocery preferences
- `backend/src/routes/v2/index.ts` - Fixed route mounting
- `frontend/lib/grocery-preferences-service.ts` - New preferences service
- `frontend/lib/user-preferences-service.ts` - Updated API endpoints
- `frontend/components/settings/grocery-preferences.tsx` - Settings UI
- `frontend/components/quick-input.tsx` - Enhanced with preferences
- `frontend/components/top-navigation-bar.tsx` - Enhanced with preferences
- `frontend/config.ts` - Fixed all settings endpoints
- `frontend/app/settings/layout.tsx` - Added GroceryProvider
- `frontend/app/dashboard/groceries/page.tsx` - Added settings button

---

## Grocery List Collaboration Architecture - Implemented 2025-05-27

**Problem:**
- Users needed to share grocery lists with family members for collaborative shopping
- Required real-time collaboration with role-based permissions
- Needed seamless integration with existing grocery list functionality
- Required email-based user identification and invitation system

**Solution:**
1. **Extended Existing Models:**
   - Enhanced the existing `GroceryList` model with collaboration features instead of creating separate shared lists
   - Added `isShared`, `collaborators`, and `shareSettings` fields to the existing schema
   - Implemented role-based permissions: Owner (full control), Editor (add/edit items), Viewer (view only)
   - Used email-based collaborator identification for user-friendly invitations

2. **V2 API Implementation:**
   - Created comprehensive V2 collaboration endpoints:
     - `POST /api/groceries/share` - Enable sharing with settings
     - `POST /api/groceries/unshare` - Disable sharing
     - `POST /api/groceries/collaborators` - Add collaborators
     - `DELETE /api/groceries/collaborators/:id` - Remove collaborators
     - `GET /api/groceries/shared` - Browse shared lists
     - `GET /api/groceries/invitations/pending` - Manage invitations
   - Applied proper validation middleware and authentication to all routes
   - Implemented comprehensive error handling with specific error messages

3. **Frontend Component Architecture:**
   - Created modular UI components:
     - `GroceryCollaborationPanel` - Main collaboration interface
     - `SharedListsBrowser` - Browse and manage shared lists
     - `GroceryListHeader` - Context indicator showing current list ownership
   - Enhanced `GroceryContext` with collaboration state management
   - Implemented seamless switching between personal and shared lists

4. **Role-Based Access Control:**
   - Owner: Can enable/disable sharing, manage collaborators, modify settings
   - Editor: Can add, edit, and delete grocery items
   - Viewer: Can only view items, cannot modify
   - Proper validation on both frontend and backend for role permissions

5. **Email Integration:**
   - Notification system for collaborator invitations
   - User lookup by email address for adding collaborators
   - Requirement for users to exist in system before being added (security measure)

**Technical Patterns:**
```typescript
// Backend: Extended existing model instead of creating new one
const groceryListSchema = new Schema({
  // Existing fields...
  isShared: { type: Boolean, default: false },
  collaborators: [{
    userId: { type: Schema.Types.ObjectId, ref: 'User', required: true },
    role: { type: String, enum: ['editor', 'viewer'], required: true },
    joinedAt: { type: Date, default: Date.now },
    invitedBy: { type: Schema.Types.ObjectId, ref: 'User', required: true }
  }],
  shareSettings: {
    allowCollaboratorInvites: { type: Boolean, default: false },
    requireApprovalForEdits: { type: Boolean, default: false },
    notifyOnChanges: { type: Boolean, default: true }
  }
});

// Frontend: Context-based state management
const GroceryContext = createContext({
  // Existing grocery state...
  groceryList: null,
  isShared: false,
  collaborators: [],
  userRole: null,
  // Collaboration methods...
  enableSharing: (settings) => Promise<void>,
  addCollaborator: (email, role) => Promise<void>
});
```

**Lessons Learned:**
- Extending existing models is often better than creating parallel systems
- Email-based user identification provides familiar UX for invitations
- Role-based permissions should be validated on both frontend and backend
- Comprehensive error messages improve debugging and user experience
- Context-based state management scales well for complex collaborative features
- V2 API routes provide clean separation while maintaining existing functionality

**Key Files:**
- `backend/src/models/GroceryList.ts` - Extended model with collaboration
- `backend/src/controllers/groceryController.ts` - Collaboration methods
- `backend/src/routes/v2/groceries.ts` - V2 collaboration routes
- `frontend/components/grocery/grocery-collaboration-panel.tsx` - Main UI
- `frontend/lib/grocery-context.tsx` - Enhanced state management
- `frontend/lib/grocery-service.ts` - Collaboration API methods

---

## Mobile Shared Groceries Architecture - Implemented 2024-12-30

**Problem:**
- Mobile app needed complete shared groceries functionality matching the web app
- Required collaboration tracking (who added, checked, modified items)
- Needed popup-based UI design following scope document specifications
- Required seamless integration with existing grocery provider and UI components
- Backend collaboration features existed but mobile app couldn't access them

**Solution:**
1. **Enhanced Data Models with Collaboration Tracking:**
   - Extended `GroceryItem` model with collaboration fields:
     ```dart
     class GroceryItem {
       // Existing fields...
       final CollaboratorInfo? lastModifiedBy;
       final CollaboratorInfo? checkedBy;
       final DateTime? checkedAt;
       final CollaboratorInfo? addedBy;
     }
     ```
   - Created comprehensive `GroceryList` model with collaboration metadata
   - Implemented `GroceryListInvitation` model for invitation management
   - Added `CollaboratorInfo` model for user tracking across collaboration features

2. **Popup-Based UI Architecture:**
   - Created `CollaborationPanel` with tabbed interface (Invitations, Management, Sharing)
   - Implemented `SharedListSelector` as modal popup for list switching
   - Enhanced main `GroceriesScreen` with collaboration buttons in AppBar
   - Used `showModalBottomSheet` with `DraggableScrollableSheet` for responsive popups
   - Followed scope document design: no separate routes, all collaboration via popups

3. **Provider State Management Enhancement:**
   - Extended `GroceryProvider` with collaboration state:
     ```dart
     class GroceryState {
       // Existing fields...
       final List<GroceryList> sharedLists;
       final List<GroceryListInvitation> pendingInvitations;
       final String? currentListOwnerId;
       final GroceryList? currentGroceryList;
     }
     ```
   - Added methods for shared list operations: `switchToSharedList()`, `switchToPersonalList()`
   - Implemented invitation management: `acceptInvitation()`, `declineInvitation()`
   - Added collaborator management: `addCollaborator()`, `removeCollaborator()`

4. **Service Layer Integration:**
   - Updated `GroceryService` with all collaboration endpoints using existing v2 API
   - Added `listOwnerId` parameter support for shared list operations
   - Implemented invitation management methods
   - Added collaboration metadata fetching and management

5. **Visual Collaboration Indicators:**
   - Enhanced `GroceryListItem` to show collaboration info when viewing shared lists
   - Added "SHARED" indicator in AppBar title when viewing shared lists
   - Implemented collaboration info display (who added, checked, modified items)
   - Added visual badges for pending invitations and shared list availability

**Technical Patterns:**
```dart
// Provider: Collaboration state management
class GroceryNotifier extends StateNotifier<GroceryState> {
  Future<void> switchToSharedList(String listOwnerId) async {
    state = state.copyWith(
      isLoading: true,
      currentListOwnerId: listOwnerId,
    );

    await fetchGroceries(listOwnerId: listOwnerId);
    await fetchGroceryList(listOwnerId);
  }

  Future<void> addCollaborator(String email, CollaboratorRole role) async {
    await _groceryService.addCollaborator(email, role);
    await fetchGroceryList(); // Refresh list metadata
  }
}

// UI: Popup-based collaboration access
void _showCollaborationPanel() {
  showModalBottomSheet(
    context: context,
    isScrollControlled: true,
    builder: (context) => DraggableScrollableSheet(
      initialChildSize: 0.7,
      minChildSize: 0.5,
      maxChildSize: 0.95,
      builder: (context, scrollController) => CollaborationPanel(
        scrollController: scrollController,
      ),
    ),
  );
}

// Service: Backend integration using existing endpoints
class GroceryService {
  Future<List<GroceryItem>> getGroceryItems({String? listOwnerId}) async {
    final params = listOwnerId != null ? {'listOwnerId': listOwnerId} : {};
    final response = await _apiClient.get(ApiConfig.groceriesEndpoint,
                                         queryParameters: params);
    // Backend automatically populates collaboration fields
  }
}
```

**Backend Support Verification:**
- ✅ All collaboration endpoints already implemented and functional
- ✅ GroceryItem model includes all collaboration tracking fields
- ✅ Backend properly populates collaboration fields when returning items
- ✅ Role-based access control fully implemented (owner/editor/viewer)
- ✅ Invitation system with token generation and email notifications
- ✅ Real-time updates via socket integration for collaborative actions

**Lessons Learned:**
- Leveraging existing backend infrastructure accelerates mobile development
- Popup-based UI provides better mobile UX than separate navigation routes
- Collaboration tracking at the data model level enables rich UI features
- Provider pattern scales well for complex collaborative state management
- Visual indicators are crucial for understanding collaboration context
- Comprehensive test coverage ensures reliability of collaboration features

**Key Files:**
- `mobile/flashtasks_mobile/lib/src/features/groceries/models/grocery_list.dart` - Collaboration model
- `mobile/flashtasks_mobile/lib/src/features/groceries/models/grocery_invitation.dart` - Invitation model
- `mobile/flashtasks_mobile/lib/src/features/groceries/widgets/collaboration_panel.dart` - Main UI
- `mobile/flashtasks_mobile/lib/src/features/groceries/widgets/shared_list_selector.dart` - List switching
- `mobile/flashtasks_mobile/lib/src/features/groceries/providers/grocery_provider.dart` - State management
- `mobile/flashtasks_mobile/lib/src/core/services/grocery_service.dart` - API integration
- `mobile/flashtasks_mobile/lib/src/features/groceries/screens/groceries_screen.dart` - Enhanced main screen

---

## Default Values for AI-Generated Content - Implemented 2025-05-16

**Problem:**
- Tasks created via QuickAdd (using AI) were not showing priority border colors in the mobile app
- The AI prompt was designed to only set priority when specific keywords were detected, otherwise returning null
- The mobile UI component showed no border when priority was null, creating inconsistency with manually created tasks
- Users expected visual cues like priority borders for all tasks regardless of creation method

**Solution:**
1. **Backend Default Value Application:**
   - Modified the `aiController.ts` file to ensure all tasks have a priority value during creation:
     ```typescript
     const taskToSave = new Task({
       // other fields...
       priority: priority || 'Medium', // Apply default 'Medium' priority if AI returns null
       // other fields...
     });
     ```
   - This approach guarantees that even when the AI returns null for a field, the task still gets a sensible default value
   - The default is applied during object creation before saving to the database

2. **Why This Approach Was Chosen:**
   - **Preserving AI Behavior:** We kept the AI prompt as is, allowing it to make nuanced decisions about when to set priorities
   - **Consistent UI:** By ensuring a default at the backend level, all UI components receive consistent data
   - **Single Point of Change:** The fix required modifying only one line in the backend rather than changing multiple components
   - **Robust Solution:** This approach handles both current and future AI responses that might have null values

3. **When to Apply This Pattern:**
   - When AI-generated content is displayed in UI components that expect non-null values
   - For fields that have clear, sensible defaults (like 'Medium' for priority)
   - When you want to maintain AI behavior while ensuring UI consistency
   - When the default is a business rule that should be enforced at the data level, not just the UI level

**Lessons Learned:**
- AI outputs should be validated and normalized before storing in the database
- Essential fields should always have sensible defaults, even when using AI generation
- UI components should be designed to handle potentially null values gracefully
- Prefer fixing data integrity issues at the backend level rather than compensating in the UI
- Small UI inconsistencies can impact user experience and perception of quality

**Key Files:**
- `backend/src/controllers/aiController.ts` - Where the fix was implemented
- `backend/src/prompts/aiPrompts.yaml` - The AI prompt that specifies when to set priority
- `mobile/flashtasks_mobile/lib/src/features/tasks/widgets/task_item.dart` - The UI component affected
- `mobile/flashtasks_mobile/lib/src/core/config/app_theme.dart` - Contains the priority color logic

---

## Mobile Permission Handling - Implemented 2025-05-15

**Problem:**
- Mobile app requires access to device features (location, microphone) that need explicit user permission
- Permissions must be handled differently on Android and iOS platforms
- Permission requests need clear explanations to maximize acceptance rates
- App needs to handle cases where permissions are denied (temporarily or permanently)
- Permission state tracking should be centralized and consistent

**Solution:**
1. **Platform-specific Permission Declarations:**
   - **Android:** Added permission declarations in `AndroidManifest.xml`:
     ```xml
     <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
     <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
     <uses-permission android:name="android.permission.RECORD_AUDIO" />
     ```
   - **iOS:** Added usage descriptions in `Info.plist`:
     ```xml
     <key>NSLocationWhenInUseUsageDescription</key>
     <string>FlashTasks needs location access to provide location-based tasks and reminders</string>
     <key>NSMicrophoneUsageDescription</key>
     <string>FlashTasks needs microphone access for voice notes and commands</string>
     ```

2. **Centralized Permission Service:**
   - Created a `PermissionsService` class with static methods for:
     - Requesting individual permissions (`requestLocationPermission()`, `requestMicrophonePermission()`)
     - Requesting all permissions at once (`requestAllPermissions()`)
     - Checking permission status (`hasLocationPermission()`, `hasMicrophonePermission()`)
     - Opening app settings for manual permission granting (`openSettings()`)
   - Used the `permission_handler` package for cross-platform permission management

3. **User-Friendly Permission Dialog:**
   - Created a reusable `PermissionRequestDialog` widget with:
     - Clear title and icon representing the permission type
     - Detailed explanation of why the permission is needed
     - Options to allow or deny the permission
     - Follow-up dialog for permanently denied permissions with settings navigation
   - Implemented helper methods for common permission types:
     - `showLocationPermission()`
     - `showMicrophonePermission()`

4. **App Integration:**
   - **On Startup:** Added permission requests in `main.dart` to request permissions during app initialization
   - **Post-Frame:** Added permission status checking in `app.dart` using `WidgetsBinding.instance.addPostFrameCallback`
   - **Feature-Based:** Prepared for feature-specific permission checks before using location or microphone APIs

**Diagram:**
```
┌───────────────┐      ┌───────────────────┐      ┌────────────────────┐
│ Platform      │      │ PermissionService │      │ UI Components      │
│ Declarations  │──────┼> Static Methods   │──────┼> PermissionDialog  │
└───────────────┘      └───────────────────┘      └────────────────────┘
       ▲                         ▲                           ▲
       │                         │                           │
       └─────────────────────────┼───────────────────────────┘
                                 │
                        ┌────────────────┐
                        │ App Components │
                        │ - main.dart    │
                        │ - app.dart     │
                        │ - feature      │
                        │   components   │
                        └────────────────┘
```

**Lessons Learned:**
- Permission handling should be consistent across the application
- Clear explanations improve user acceptance of permission requests
- Centralized permission services improve maintainability
- Early permission requests ensure features work when needed
- Graceful handling of denied permissions improves user experience

**Key Files:**
- `android/app/src/main/AndroidManifest.xml` - Android permission declarations
- `ios/Runner/Info.plist` - iOS permission usage descriptions
- `lib/src/core/services/permissions_service.dart` - Permission handling service
- `lib/src/core/widgets/permission_request_dialog.dart` - UI component for permission requests
- `lib/main.dart` - Initial permission requests
- `lib/src/app.dart` - Permission status verification

---

## Frontend Coordinate Type Handling - Fixed 2025-10-02

**Problem:**
- Frontend components using map functionality were experiencing TypeScript errors due to coordinate format mismatches
- The API returned location coordinates in multiple formats which didn't always match component expectations
- TypeScript errors appeared during builds, preventing successful deployment

**Investigation:**
- The `Location` model in frontend components expected coordinates in a specific GeoJSON format: `{ type: 'Point'; coordinates: [number, number] }`
- The API's `LocationModel` allowed coordinates to be either:
  - GeoJSON format: `{ type: 'Point'; coordinates: [number, number] }`
  - Direct coordinate format: `{ latitude: number; longitude: number }`
- TypeScript errors occurred when trying to assign `LocationModel[]` to `Location[]` state variables
- The issue affected several parts of the locations page including data fetching, adding locations, and editing locations

**Solution:**
1. **Type Guard Functions:**
   - Created type guard functions to safely check coordinate formats:
     ```typescript
     const isDirectCoordinates = (coords: any): coords is { latitude: number; longitude: number } => {
       return coords && 'latitude' in coords && 'longitude' in coords;
     };

     const isGeoPoint = (coords: any): coords is { type: 'Point'; coordinates: [number, number] } => {
       return coords && 'type' in coords && coords.type === 'Point' && 'coordinates' in coords;
     };
     ```

2. **Coordinate Format Conversion:**
   - Implemented format conversion when setting state:
     ```typescript
     setLocations(locationsData.map(loc => {
       const coordinates = isGeoPoint(loc.coordinates)
         ? loc.coordinates
         : {
             type: 'Point' as const,
             coordinates: [
               isDirectCoordinates(loc.coordinates)
                 ? loc.coordinates.longitude
                 : 0,
               isDirectCoordinates(loc.coordinates)
                 ? loc.coordinates.latitude
                 : 0
             ] as [number, number]
           };

       return {
         ...loc,
         coordinates
       } as Location;
     }));
     ```

3. **Consistent Pattern Application:**
   - Applied the same pattern for:
     - Initial data loading from API
     - Adding new locations
     - Updating existing locations
     - Any other interactions with location data

**Lessons Learned:**
- Always use type guards when handling data that might have different formats
- Implement explicit conversion between different coordinate formats rather than relying on type assertions
- Apply consistent patterns throughout components dealing with the same data types
- Consider implementing shared utilities for coordinate conversion if used in multiple components

**Key Files:**
- `frontend/app/dashboard/locations/page.tsx`: Main component implementing the solution
- `frontend/lib/types/location.model.ts`: Model definitions for API types
- `frontend/lib/location-service.ts`: Service handling API calls

---

## Security Implementation - Fixed 2025-08-22

**Problem:**
- The application needed improved security features according to guidelines in `docs/security-scope.md`
- Authentication was using simple JWT tokens without refresh capability
- State-changing routes lacked CSRF protection
- Input validation was inconsistent across controllers
- Cookies were not properly secured

**Solution:**
1. **Refresh Token Implementation:**
   - Updated User model to store refresh tokens with metadata (expiry, user agent, IP address)
   - Implemented token rotation for better security
   - Added refresh token endpoint and logout functionality
   - Used HTTP-only cookies for token storage

2. **CSRF Protection:**
   - Added CSRF middleware to protect state-changing routes
   - Created a CSRF token endpoint for the frontend
   - Implemented both cookie-based and memory-based token validation
   - Added detailed error messages for CSRF validation failures

3. **Input Validation:**
   - Created a validation middleware using express-validator
   - Added comprehensive validation to all routes (auth, users, tasks, categories)
   - Implemented proper error handling for validation failures

4. **Role-Based Access Control:**
   - Enhanced the RBAC middleware to properly check user permissions
   - Applied RBAC to all admin routes
   - Improved error handling for unauthorized access

**Outcome:**
- All security features are now working correctly
- Authentication is more secure with refresh tokens
- State-changing routes are protected against CSRF attacks
- Input validation is consistent across all controllers
- Cookies are properly secured with appropriate settings

---

## Task Filtering Logic (Categories & Reminders) - Fixed 2025-04-18

**Problem:**
- The multi-select category filter in `frontend/components/task-filter-menu.tsx` was not applying correctly; selecting categories had no effect on the displayed tasks.
- The "Has Reminder" filter was also not working as intended. It was filtering based on the existence of a `deadline`, but the requirement was to filter based on the actual `reminders` array, distinct from just having a deadline.

**Investigation:**
- Frontend state management (`task-filter-menu.tsx`, `dashboard-context.tsx`, `page.tsx`) appeared correct in handling filter state changes and passing them down.
- The API service (`task-service.ts`) correctly translated filter state into query parameters (`categories`, `reminderFilter`).
- The backend controller (`taskController.ts`) logic for handling individual filters (`$in` for categories, `$exists` for deadline/reminders) looked correct in isolation.
- Logging revealed the `categories` query parameter was missing when applied from the filter menu, suggesting an issue in how frontend state updates handled the switch between `categoryContextId` (sidebar) and `selectedCategoryIds` (menu).
- Further clarification revealed the need to distinguish between "Has Deadline" and "Has Reminder Set".
- The root cause for both issues was identified in the backend controller (`taskController.ts`) where the construction of the MongoDB aggregation `$match` stage using an `$and` array caused filter conditions (especially `categories: { $in: [...] }`) to be ignored or conflict when combined with other filters (like `status`).

**Solution:**
1.  **Reminder Filter:**
    *   Modified `frontend/components/task-filter-menu.tsx`: Replaced the "Has Reminder" checkbox with a radio group ("Any", "Has Deadline", "Has Reminder Set"). Updated state and `applyFilters` accordingly.
    *   Updated `TaskFilters` type in `frontend/app/dashboard/page.tsx` and `frontend/lib/task-service.ts`.
    *   Modified `frontend/lib/task-service.ts`: Updated `getTasks` to send the new `reminderFilter` query parameter.
    *   Modified `backend/src/controllers/taskController.ts`: Updated `getTasks` to read `reminderFilter` and apply the correct MongoDB filter (`{ deadline: { $exists: true, $ne: null } }` for 'hasDeadline', `{ reminders: { $exists: true, $ne: [] } }` for 'hasReminder').
2.  **Category Filter:**
    *   Modified `backend/src/controllers/taskController.ts`: Refactored `getTasks` to build the `$match` stage by applying most filter conditions directly to the `matchStage` object, rather than putting everything into an `$and` array. This ensures all active filters, including the multi-select category filter (`categories: { $in: [...] }`), are correctly combined.
    *   Refined `handleFilterChange` in `frontend/app/dashboard/page.tsx` to more robustly ensure `categoryContextId` is nulled when `selectedCategoryIds` are active.

**Key Files Modified:**
- `frontend/components/task-filter-menu.tsx`
- `frontend/app/dashboard/page.tsx`
- `frontend/lib/task-service.ts`
- `backend/src/controllers/taskController.ts`

## Mobile Authentication Architecture (Flutter) - Implemented [Date - Check Git History]

**Problem:**
- Need a robust and maintainable authentication system for the Flutter mobile app.
- State management needs to be centralized.
- Routing must react correctly to authentication state changes (including loading).

**Solution:**
1.  **State Management (`AuthProvider`):**
    *   Uses Riverpod (`NotifierProvider`) as the central hub for authentication state (`AuthState`).
    *   `AuthState` includes `isLoading` (boolean, default true), `isAuthenticated` (boolean), `user` (nullable User object), `error` (nullable String).
    *   `AuthNotifier` handles logic for `login`, `register`, `logout`, and potentially `checkAuthStatus` on startup.
    *   Methods return `Future<bool>` indicating success/failure.
    *   Handles errors internally and updates the `error` state property.
    *   Provides a `clearError` method or parameter in `copyWith`.
2.  **UI Integration (`LoginScreen`, etc.):**
    *   Widgets listen (`ref.watch`) to the `authProvider` to get the current `AuthState`.
    *   UI displays loading indicators based on `authState.isLoading`.
    *   UI displays error messages (e.g., via `SnackBar`) based on `authState.error`.
    *   Input fields and buttons interact with `AuthNotifier` methods (`ref.read(authProvider.notifier).login(...)`).
    *   Local UI state for authentication errors is avoided; the central `AuthProvider` state is the single source of truth.
3.  **Routing (`GoRouter`):**
    *   Configured with `refreshListenable: authProvider` to react to state changes.
    *   `redirect` logic inspects `authState` (`isLoading`, `isAuthenticated`) and current route location.
    *   Handles initial loading: Keeps user on a splash/loading screen while `authState.isLoading` is true.
    *   Handles transitions: Redirects from splash to login/dashboard after loading completes based on `isAuthenticated`.
    *   Redirects unauthenticated users trying to access protected routes to the login screen.
    *   Redirects authenticated users trying to access the login screen to the dashboard/home screen.

**Diagram:**

```mermaid
flowchart TD
    subgraph UI Layer
        LoginScreen -- Reads state/Calls methods --> AuthProvider
        SplashScreen -- Reads state --> AuthProvider
        Dashboard -- Reads state --> AuthProvider
    end

    subgraph State Layer
        AuthProvider(AuthProvider - Riverpod NotifierProvider) -- Manages --> AuthState{AuthState isLoading, isAuthenticated, user, error}
    end

    subgraph Routing Layer
        GoRouter -- Listens (refreshListenable) --> AuthProvider
        GoRouter -- Uses redirect logic based on --> AuthState
    end

    subgraph Service Layer
        AuthNotifier -- Calls --> ApiClient/AuthService
    end

    AuthProvider --> AuthNotifier
    UI_Layer -- Navigates via --> GoRouter

```

**Outcome:**
- Clear separation of concerns: Auth logic, State, UI, and Routing are distinct.
- Centralized and predictable state management using Riverpod.
- Robust routing that correctly handles loading and authentication states.
- Improved user experience with clear loading indicators and error messages.
- Enhanced testability and maintainability.

**Key Packages:**
- `flutter_riverpod` / `riverpod_annotation`
- `go_router`
- `flutter_secure_storage` (Likely used by AuthNotifier/ApiClient)

## Standardized API Response Format - Implemented 2025-05-21

**Problem:**
- API responses were inconsistent across different controllers and endpoints
- Error handling was implemented differently in various parts of the application
- Frontend and mobile clients had to handle multiple response formats
- Debugging was difficult due to inconsistent error information
- Some responses lacked success indicators or proper error details

**Solution:**
1. **Standardized Response Structure:**
   - Implemented a consistent format for all API responses:
     - Success responses: `{ success: true, data: {...} }`
     - Error responses: `{ success: false, error: { message: '...', code: 'ERROR_CODE' } }`
     - List responses: `{ success: true, data: [...], pagination: { currentPage, totalPages, pageSize, totalItems } }`
   - Added `success` field to all responses to provide a clear indicator of operation status
   - Structured error responses with both human-readable messages and machine-readable error codes
   - Standardized pagination format with consistent metadata fields

2. **Controller Updates:**
   - Updated all V2 controllers to follow the standardized format:
     - authController.ts
     - categoryController.ts
     - locationController.ts
     - taskController.ts
     - userController.ts
     - userSettingsController.ts
   - Modified non-V2 controllers to use the standardized format for backward compatibility
   - Added specific error codes for different error types (authentication, validation, not found, etc.)

3. **Client-Side Parsing:**
   - Enhanced the BaseService class in the frontend to handle the standardized format
   - Updated mobile app parsers to extract data from the standardized response structure
   - Implemented backward compatibility to handle both old and new response formats during transition

4. **Error Code Standardization:**
   - Created a set of standard error codes for common error conditions:
     - `AUTHENTICATION_REQUIRED`: User is not authenticated
     - `RESOURCE_NOT_FOUND`: Requested resource doesn't exist
     - `VALIDATION_ERROR`: Input validation failed
     - `SERVER_ERROR`: Internal server error
     - `FORBIDDEN_OPERATION`: User doesn't have permission
     - `DUPLICATE_RESOURCE`: Resource already exists
     - `INVALID_ID_FORMAT`: Invalid MongoDB ID format

**Outcome:**
- Consistent API responses across all endpoints
- Improved error handling with specific error codes
- Simplified client-side parsing with predictable response structure
- Better debugging experience with standardized error information
- Enhanced user feedback with clear error messages
- Reduced code duplication in client-side parsers
- Foundation for better API documentation and testing

**Key Files:**
- `backend/src/controllers/v2/*.ts`: All V2 controllers
- `frontend/lib/base-service.ts`: Base service class for API communication
- `mobile/flashtasks_mobile/lib/src/core/services/api_client.dart`: Mobile API client
- `backend/src/middleware/error-handler.ts`: Global error handler

## Mobile API Structure (V2 Endpoints) - Implemented 2025-09-21

**Problem:**
- The existing API was not optimized for mobile clients, returning excessive data that increased bandwidth usage
- Error responses were inconsistent across endpoints, making error handling difficult in mobile apps
- Pagination was implemented inconsistently across different endpoints
- Implementing changes directly to existing V1 endpoints would risk breaking the web frontend

**Solution:**
1. **Separate API Version:**
   - Created a new `/api/v2` namespace for mobile-optimized endpoints
   - Mounted all V2 routes through a central index file that applies authentication middleware
   - Maintained the original V1 endpoints for backward compatibility

2. **Standardized Pagination:**
   - Created a reusable pagination helper utility (`paginationHelper.ts`)
   - Implemented consistent pagination parameters (page, limit) across all endpoints
   - Used a standardized response format including pagination metadata (currentPage, totalPages, pageSize, totalItems)
   - **Standard Response Structure:** List endpoints return `{ data: [ItemType], pagination: { currentPage, totalPages, pageSize, totalItems } }`. Clients (like Flutter `TaskService`) **must** parse this structure correctly, extracting the list from the `data` property.

3. **Consistent Error Handling:**
   - Enhanced the global error handler to provide detailed error information
   - Standardized error response format with success indicator, error code, and message
   - Categorized errors (validation, authentication, not found, etc.) with appropriate status codes

4. **Optimized Payloads:**
   - Reduced response size by returning only fields needed for mobile views
   - Used mobile-friendly property names (e.g., `isDone` instead of `completed`)
   - Selectively populated related documents (categories, locations) with only essential fields
   - Transformed backend data models to simpler mobile-friendly formats

5. **Documentation:**
   - Created comprehensive API documentation in `backend/src/routes/v2/README.md`
   - Included examples of response formats and error handling

**Outcome:**
- Mobile clients now have optimized API endpoints that reduce bandwidth usage
- Consistent error handling and response formats improve mobile development experience
- The web frontend continues to function with the original V1 endpoints
- The backend is ready to support the Flutter mobile app development

**Key Files:**
- `backend/src/utils/paginationHelper.ts`: Standardized pagination utility
- `backend/src/routes/v2/index.ts`: Central routing for V2 endpoints
- `backend/src/routes/v2/tasks.ts`, `categories.ts`, `locations.ts`: Route definitions
- `backend/src/controllers/v2/*`: Mobile-optimized controllers
- `backend/src/app.ts`: Error handler enhancement and route mounting
- `backend/src/routes/v2/README.md`: API documentation

## Mobile Authentication Implementation - Fixed 2025-09-25

**Problem:**
- The application needed to support both web and mobile authentication flows
- Mobile clients required token-based authentication instead of cookie-based
- Existing web authentication couldn't be disrupted
- CSRF protection needed to be bypassed for mobile token-based requests
- The mobile app was experiencing authentication issues, particularly being logged out unexpectedly

**Solution:**
1. **Dual Authentication Flow:**
   - Enhanced login endpoint to return both cookies (web) and tokens (mobile) in response
   - Modified auth middleware to check both Bearer token and cookie authentication
   - Implemented token refresh with rotation for security
   - Added support for refresh token in request body/header/cookie

2. **Mobile Token Management:**
   - Updated User model to store and manage refresh tokens with metadata
   - Implemented token rotation on refresh for better security
   - Added proper token invalidation during logout
   - Enhanced error responses with specific codes for token issues

3. **CSRF Protection:**
   - Modified CSRF middleware to bypass checks for Bearer token requests
   - Maintained CSRF protection for cookie-based web authentication
   - Added logging for CSRF bypass decisions

4. **Error Handling:**
   - Standardized error responses with specific codes
   - Enhanced logging for authentication failures
   - Added proper validation for token formats

5. **Mobile Authentication Fixes (September 2025):**
   - Corrected API endpoint configuration in `api_config.dart` (changed from `/users/me` to `/api/users/profile`)
   - Enhanced `User.fromJson` to handle different ID field names (`_id` from MongoDB or `id` from normalized API)
   - Improved error handling in `getCurrentUser` with better context in logs and specific handling for different error types
   - Modified `AuthNotifier._init()` to keep users authenticated when they have valid tokens even if profile fetch fails
   - Only clearing tokens for actual authentication errors (401/403), not for network or configuration issues

**Outcome:**
- Mobile clients can authenticate using Bearer tokens
- Web application continues to work with cookie-based auth
- Refresh token rotation provides enhanced security
- Clear error responses help client-side error handling
- CSRF protection remains effective for web clients
- Mobile users remain authenticated during page refreshes and temporary network issues
- More detailed error logging aids in troubleshooting

**Key Files:**
- `backend/src/controllers/authController.ts`
- `backend/src/middleware/auth.ts`
- `backend/src/app.ts`
- `backend/src/models/User.ts`
- `mobile/flashtasks_mobile/lib/src/core/config/api_config.dart`
- `mobile/flashtasks_mobile/lib/src/core/services/auth_service.dart`
- `mobile/flashtasks_mobile/lib/src/features/auth/models/user.dart`
- `mobile/flashtasks_mobile/lib/src/features/auth/providers/auth_provider.dart`

## Authentication Architecture

### Authentication Flow
1. **Dual Authentication Support**
   - Web application: Session-based authentication
   - Mobile/API clients: JWT-based authentication
   - Shared security middleware layer

2. **Token Management**
   - JWT generation with secure algorithm
   - Automatic token rotation
   - Refresh token mechanism
   - Token blacklisting for revocation

3. **Mobile Authentication Robustness**
   - Resilient to network interruptions and temporary server issues
   - Correct endpoint configuration for profile fetching
   - Smart error handling that distinguishes between auth failures and connection issues
   - Consistent handling of backend data models (e.g., handling both `_id` and `id` fields)

4. **Security Layers**
   ```
   [Client Request]
      ↓
   Rate Limiting
      ↓
   CORS Check
      ↓
   Security Headers
      ↓
   CSRF Protection
      ↓
   Authentication
      ↓
   Authorization
      ↓
   [Route Handler]
   ```

### Security Patterns
1. **Rate Limiting**
   - Per-IP and per-route limits
   - Configurable thresholds
   - Redis-based tracking

2. **Error Handling**
   - Standardized error responses
   - Security event logging
   - Rate limit notifications

3. **Monitoring**
   - Authentication success/failure metrics
   - Token rotation tracking
   - Response time monitoring
   - Security event alerts

### Integration Points
- Frontend authentication service
- Mobile API authentication middleware
- Security monitoring system
- Logging infrastructure

---

## Mobile Groceries Tab Implementation - Implemented 2025-09-28

**Problem:**
- The mobile app needed a comprehensive grocery management feature matching the web frontend functionality
- The implementation needed to handle different API response formats and provide a mobile-friendly UI
- The feature required proper state management, error handling, and offline support
- The UI needed to support various view modes, filtering, and sorting options

**Solution:**
1. **Model Layer:**
   - Created `GroceryItem` and `GroceryCategoryInfo` models with robust JSON parsing
   - Implemented `GroceryInsights` model for AI-generated insights
   - Designed models to handle different API response formats (both `_id` and `id` fields)
   - Added color mapping functionality to convert backend color strings to Flutter Colors

2. **Service Layer:**
   - Developed `GroceryService` for API interactions with comprehensive error handling
   - Updated `ApiConfig` with grocery-related endpoints
   - Implemented methods for fetching, adding, updating, and deleting grocery items
   - Added support for suggestions and insights API integration

3. **State Management:**
   - Implemented `GroceryProvider` using Riverpod's StateNotifier pattern
   - Created a comprehensive `GroceryState` class with loading, error, and data states
   - Added optimistic updates with proper error recovery
   - Implemented methods for all CRUD operations with proper error handling

4. **UI Components:**
   - Created `GroceryListItem` widget for displaying items with check/delete functionality
   - Implemented `InlineAddItem` widget for adding items within categories
   - Developed `EditGroceryItemDialog` for modifying grocery items
   - Created the main `GroceriesScreen` with tabs, sorting, and filtering options

5. **Navigation:**
   - Updated app router to include the groceries screen
   - Integrated with the dashboard shell for proper navigation
   - Added tab-based navigation within the groceries screen

**Outcome:**
- The groceries tab is now fully functional in the mobile app
- Users can add, edit, delete, and check off grocery items
- Items can be grouped by category or sorted alphabetically
- The UI provides filtering options for current, completed, or all items
- AI suggestions and insights are integrated into the experience
- The implementation follows Flutter best practices and matches the web frontend functionality

**Key Files:**
- `mobile/flashtasks_mobile/lib/src/features/groceries/models/grocery_item.dart`
- `mobile/flashtasks_mobile/lib/src/features/groceries/models/grocery_insights.dart`
- `mobile/flashtasks_mobile/lib/src/core/services/grocery_service.dart`
- `mobile/flashtasks_mobile/lib/src/features/groceries/providers/grocery_provider.dart`
- `mobile/flashtasks_mobile/lib/src/features/groceries/widgets/grocery_list_item.dart`
- `mobile/flashtasks_mobile/lib/src/features/groceries/widgets/inline_add_item.dart`
- `mobile/flashtasks_mobile/lib/src/features/groceries/widgets/edit_grocery_item_dialog.dart`
- `mobile/flashtasks_mobile/lib/src/features/groceries/screens/groceries_screen.dart`
- `mobile/flashtasks_mobile/lib/src/core/routing/app_router.dart`

**Key Learnings:**
- Robust model classes with flexible parsing are essential for handling different API response formats
- Optimistic updates with proper error recovery provide a responsive user experience
- Riverpod's StateNotifier pattern works well for complex state management
- Proper separation of concerns (models, services, providers, UI) leads to maintainable code
- Mobile UI adaptations (tabs, bottom sheets, dialogs) provide a better user experience than direct web UI ports
- Comprehensive error handling is crucial for a robust mobile app

---

## Mobile App Authentication with Consolidated API - Implemented 2025-09-30

**Problem:**
- After API consolidation, the mobile app was unable to login successfully
- The login response from the API was successful (status code 200), but the AuthNotifier was reporting a login failure
- The issue was due to changes in the response format after API consolidation
- The mobile app expected a response with nested `tokens` and `user` objects, but the consolidated API returned a flattened structure

**Solution:**
1. **Updated AuthService Login Method:**
   - Modified the `login` method in `AuthService` to handle both response formats
   - Added support for the new format (direct user data with tokens) and the old format (separate user and tokens objects)
   - Added detailed logging to help diagnose authentication issues

2. **Updated Token Refresh Logic:**
   - Modified the `refreshTokens` method to handle both token response formats
   - Added support for direct token fields and nested token objects

3. **Updated User Profile Retrieval:**
   - Modified the `getCurrentUser` method to handle both `userId` and `id` field formats
   - Added support for different user data structures

**Outcome:**
- Mobile app can now successfully authenticate with the consolidated API
- Login, token refresh, and user profile retrieval work correctly
- Backward compatibility with old response formats is maintained
- Added detailed logging for easier debugging

**Key Files:**
- `mobile/flashtasks_mobile/lib/src/core/services/auth_service.dart`
- `mobile/flashtasks_mobile/lib/src/features/auth/notifiers/auth_notifier.dart`
- `mobile/flashtasks_mobile/lib/src/core/config/api_config.dart`

**Key Learnings:**
- When consolidating APIs, it's important to consider the impact on client applications
- Handling multiple response formats can provide backward compatibility during transitions
- Detailed logging is essential for diagnosing authentication issues
- Response format changes require updates to all affected client methods

## API Consolidation - Implemented 2025-09-30

**Problem:**
- The application had two separate APIs (V1 and V2) serving different clients
- V1 API was used by the web frontend and V2 API was used by the mobile app
- This dual API approach led to code duplication and maintenance challenges
- Feature parity between web and mobile was difficult to maintain
- Changes to one API often required similar changes to the other

**Solution:**
1. **Backend Consolidation:**
   - Modified backend controllers in `backend/src/controllers/v2/` to return full data models instead of limited fields
   - Renamed methods like `getMobileTasks` to `getTasks`, `getMobileTaskById` to `getTaskById`, etc.
   - Removed `.select()` statements that limited fields and ensured proper population of related documents
   - Changed `app.use('/api/v2', v2Routes)` to `app.use('/api', v2Routes)` in app.ts
   - Removed all old V1 route mounts while keeping shared essential routes like auth, csrf-token, and ping

2. **Frontend Adaptation:**
   - Updated endpoint definitions in `frontend/config.ts` to point to the correct paths
   - Modified BaseService class to handle both legacy and new response formats
   - Updated task-service.ts, category-service.ts, location-service.ts, and user-service.ts to use the new endpoints
   - Added support for the new response format with `success` and `data` fields
   - Maintained backward compatibility with legacy response formats

3. **Mobile App Adaptation:**
   - Removed the `/v2` path segment from all endpoint definitions in `mobile/flashtasks_mobile/lib/src/core/config/api_config.dart`
   - Updated endpoint paths to match the new API structure
   - Changed `taskCompletionEndpoint` to use `toggle` instead of `complete`

**Outcome:**
- Unified API structure for both web and mobile clients
- Simplified API maintenance with a single set of endpoints
- Improved consistency in response formats
- Reduced code duplication in the backend
- Better feature parity between web and mobile applications
- Some test failures related to CSRF protection and admin routes need to be addressed

**Key Files:**
- `backend/src/controllers/v2/taskController.ts`
- `backend/src/routes/v2/tasks.ts`
- `backend/src/app.ts`
- `frontend/config.ts`
- `frontend/lib/base-service.ts`
- `frontend/lib/task-service.ts`
- `frontend/lib/category-service.ts`
- `frontend/lib/location-service.ts`
- `frontend/lib/user-service.ts`
- `mobile/flashtasks_mobile/lib/src/core/config/api_config.dart`

**Key Learnings:**
- Standardized response formats are crucial for API consolidation
- Backward compatibility can be maintained through careful response handling
- Comprehensive testing is essential after API consolidation
- Proper error handling is important for a smooth transition
- API consolidation simplifies maintenance and ensures feature parity

## API Versioning and Mobile Optimization

### V2 API Design Patterns

The V2 API is designed specifically for mobile clients with these key patterns:

1. **Standardized Response Format**
   ```json
   {
     "success": true,
     "data": [...],  // Array or object with actual data
     "pagination": { // Only for paginated endpoints
       "currentPage": 1,
       "totalPages": 5,
       "pageSize": 15,
       "totalItems": 72
     }
   }
   ```

2. **Standardized Error Format**
   ```json
   {
     "success": false,
     "error": {
       "message": "User-friendly error message",
       "code": "ERROR_CODE",
       "details": {} // In development only
     }
   }
   ```

3. **Mobile-Optimized Payloads**
   - Reduced field sets for list views
   - Only essential data returned for mobile screens
   - Hierarchical data structures for efficient rendering

4. **Category Management Pattern**
   - Hierarchical structure maintained through parent-child relationships
   - Top-level categories have `parentCategory: null`
   - Subcategories have `parentCategory: ObjectId(...)` pointing to their parent
   - Special handling for predefined categories:
     - Color updates allowed for all categories
     - Name updates restricted for predefined categories with `allowUserManagement: false`
   - Consistent validation across all category operations

## Mobile V2 API Patterns - Implemented September 2025

**Problem:**
- Need to standardize API response format for mobile clients
- Ensure consistent error handling across all endpoints
- Implement proper testing patterns for V2 endpoints
- Maintain backward compatibility for web frontend

**Solution:**
1. **Standardized Response Format:**
   ```typescript
   interface V2Response<T> {
     success: boolean;
     data: T;
     error?: {
       code: string;
       message: string;
     };
   }

   interface V2ListResponse<T> extends V2Response<T[]> {
     pagination: {
       currentPage: number;
       totalPages: number;
       pageSize: number;
       totalItems: number;
     };
   }
   ```

2. **Error Handling Pattern:**
   - All errors follow the V2Response format with success: false
   - Error codes are standardized (AUTH_ERROR, VALIDATION_ERROR, etc.)
   - HTTP status codes match the error type (401, 400, etc.)
   - Detailed error messages for client debugging

3. **Testing Pattern:**
   - Mock classes for external dependencies (Dio, SecureStorage)
   - Consistent test structure across all V2 endpoint tests
   - Coverage of success and error cases
   - Verification of response format compliance

4. **Implementation Examples:**
   ```dart
   // User Model V2 Pattern
   class User {
     final String id;
     final String email;
     final String name;
     final String? avatar;  // V2: Replaced profileImageUrl
     final Map<String, dynamic>? preferences;  // V2: Added for user settings

     factory User.fromJson(Map<String, dynamic> json) {
       return User(
         id: json['_id'] ?? json['id'],  // Handle both formats
         email: json['email'],
         name: json['name'],
         avatar: json['avatar'],
         preferences: json['preferences'],
       );
     }
   }

   // Test Pattern for V2 Endpoints
   void main() {
     group('V2 Endpoint Tests', () {
       late MockDio mockDio;
       late MockSecureStorage mockStorage;
       late ServiceClass service;

       setUp(() {
         mockDio = MockDio();
         mockStorage = MockSecureStorage();
         service = ServiceClass(mockDio, mockStorage);
       });

       test('successful response handling', () async {
         // Setup mock response in V2 format
         // Verify response parsing
         // Check error handling
       });
     });
   }
   ```

5. **Category Management Pattern:**
   - Hierarchical structure maintained through parent-child relationships
   - Top-level categories have `parentCategory: null`
   - Subcategories have `parentCategory: ObjectId(...)` pointing to their parent
   - Special handling for predefined categories:
     - Color updates allowed for all categories
     - Name updates restricted for predefined categories with `allowUserManagement: false`
   - Consistent validation across all category operations
   - Mobile app handles type conversion between CategoryBasic and HierarchicalCategory models

**Outcome:**
- Consistent response format across all V2 endpoints
- Robust error handling with clear error messages
- Comprehensive test coverage
- Smooth migration path for mobile clients

## API Patterns

### V2 API Patterns

#### HTTP Methods
- **GET** - Used for retrieving resources
- **POST** - Used for creating new resources
- **PUT** - Used for updating resources (complete replacement)
- **DELETE** - Used for removing resources
- **PATCH** - Used for partial updates (not used in V2 API, replaced with PUT)

#### Grocery API Patterns
- **Grocery Item Updates:** V2 API uses PUT for updating grocery items, not PATCH
- **Category Format:** V2 API returns category as a string (e.g., "Meat") rather than an object with name and color properties
- **Quantity Handling:** Quantity is stored as a string in the database but should be displayed as a string in the UI

## API Field Name Mapping After Consolidation (2025-05-08)

**Pattern:**
- When consolidating or versioning APIs, backend field names may change (e.g., from `_id` to `id`).
- If the frontend expects a different field name (e.g., `_id`), relying on the spread operator or implicit mapping can result in silent bugs (e.g., undefined IDs in the UI, broken filtering, or selection logic).

**Best Practice:**
- Always use explicit mapping in service/model layers to translate backend field names to frontend expectations (e.g., `id` → `_id`).
- After API changes, verify all critical fields (IDs, parent references, etc.) are mapped and used consistently throughout the app.
- Add targeted logging in the UI to quickly trace data flow and catch mapping issues early.

**Example:**
- After API consolidation, the backend returned categories with `id`, but the frontend expected `_id`. This broke category filtering and selection in the web app until explicit mapping was added in the category service.

## Data Handling Patterns

### Location Data Handling

#### Coordinate Extraction and Normalization

The application uses helper functions to handle different formats of location coordinates consistently:

```typescript
// Helper function to extract coordinates safely from location objects
function getLocationCoordinates(location: any): [number, number] | null {
  if (!location) return null;

  // Handle case where location has nested coordinates with latitude/longitude
  if (location.coordinates) {
    // Format: { coordinates: { latitude, longitude } }
    if (typeof location.coordinates.latitude === 'number' &&
        typeof location.coordinates.longitude === 'number') {
      return [location.coordinates.longitude, location.coordinates.latitude];
    }
    // Format: { coordinates: [longitude, latitude] } (GeoJSON)
    if (Array.isArray(location.coordinates) && location.coordinates.length >= 2) {
      return [location.coordinates[0], location.coordinates[1]];
    }
  }

  // Handle case with direct lat/lng properties
  if (typeof location.latitude === 'number' && typeof location.longitude === 'number') {
    return [location.longitude, location.latitude];
  }

  // No valid coordinates found
  return null;
}
```

This pattern ensures consistent coordinate handling across the application, regardless of the data source or format.

#### Location ID Extraction

To handle different ID field names in location objects (from different API versions or data sources):

```typescript
// Helper function to safely extract location ID
function getLocationId(location: any): string | null {
  if (!location) return null;

  // Handle both _id and id fields
  return location._id || location.id || null;
}
```

This pattern is used consistently when comparing location references or looking up location data.

## Exception Handling Patterns

### **Error Logging Strategy**
The application uses a structured approach to error logging, combining detailed context with sanitized output:

1. **`secureLogger` Utility**:
   - Sanitizes sensitive information automatically
   - Provides consistent logging interface across application
   - Supports different log levels (log, warn, error)
   - Now contains structured logging for AI parsing errors with original input values

2. **Error Handling Layers**:
   - Controller level (catch and return HTTP error)
   - Service level (catch, log, rethrow)
   - Model level (validation errors)
   - Global error handler (last resort)

3. **Frontend Error Handling**:
   - Toast notifications for user feedback
   - Error boundaries for React component errors
   - Visual indicators for specific error types (parsing failures, validation errors)

### **AI Error Handling & Visibility**
The application employs specific patterns to handle AI-related errors:

1. **Date Parsing Robustness**:
   - The `parseDateString` function in `aiController.ts` uses a multi-stage approach:
     - Tries standard `Date.parse()`
     - Handles special relative formats ("today", "tomorrow", etc.)
     - Attempts common date formats (MM/DD/YYYY, DD/MM/YYYY)
     - Logs parsing failures with original input for analysis
   - Uses a metadata field `aiSuggestedDeadlineFailed` to track failed parsing attempts
   - Provides visual indicators in the UI when parsing fails

2. **JSON Parsing Safety**:
   - Uses `safeJsonParse` utility for all AI responses
   - Handles malformed JSON gracefully
   - Validates expected structure after parsing

3. **AI Response Validation**:
   - Type checking on parsed responses
   - Fallback values for missing fields
   - Default responses when AI fails to generate expected output

---

## Evolved AI Quick Add Pattern - Implemented 2025-05-11

**Problem:**
- The initial `processQuickAdd` AI was limited in its ability to parse complex, multi-task inputs accurately.
- It often applied extracted context (like a single deadline or category) globally to all created tasks, rather than to individual sub-tasks.
- It lacked the ability to identify implicit projects within a user's input.

**Solution (Evolved `processQuickAdd`):**
1.  **Enhanced Prompt Engineering (`backend/src/prompts/aiPrompts.yaml`):**
    *   **Sophisticated Multi-Task Segmentation:** The prompt now includes more detailed instructions and examples for the AI to identify and separate multiple distinct tasks from a single natural language input. This covers various cues like sequential indicators, conjunctions, implicit breaks based on different actions or contexts.
    *   **Per-Task Attribute Extraction:** The core change is instructing the AI to extract a full suite of attributes (`title`, `content`, `categoryNames`, `locationName`, `deadline`, `time`, `priority`, `reminders`) for *each individual task* it identifies. This ensures granularity.
    *   **Implicit Project Suggestion:** The AI is now prompted to analyze the collection of parsed tasks. If they collectively seem to represent steps towards a larger common goal, it should suggest a `suggestedProjectTitle`.
    *   **Standardized Rich JSON Output:** The prompt mandates a specific JSON output structure: a top-level object with `suggestedProjectTitle` (string or null) and a `tasks` array. The `tasks` array can contain multiple detailed task objects (each with its own full set of attributes) or a single grocery object.

2.  **Backend Controller Adaptation (`backend/src/controllers/aiController.ts`):**
    *   **Parsing New JSON Structure:** The controller logic was updated to parse this new, richer JSON output from the AI.
    *   **Per-Task Processing:** For each task object received from the AI:
        *   All specific attributes (deadline, location, categories, priority, etc.) are extracted.
        *   Relevant services (e.g., `locationSuggestionService`, date/time parsing, category mapping) are invoked using the context of that individual task.
        *   A new `Task` document is created with these granular details.
    *   **Handling `suggestedProjectTitle`:** The `suggestedProjectTitle` is extracted and included in the API response to the client, allowing the frontend to present this suggestion to the user.
    *   **Grocery Intent:** The existing logic for handling grocery items (if the AI identifies the input as `intent: "grocery"`) is maintained.

**Outcome:**
- The backend can now process much more complex and nuanced quick add inputs.
- Tasks created via quick add are more accurately detailed from the outset, with attributes like deadlines, locations, and categories applied to the correct individual sub-tasks.
- The system can proactively suggest project groupings, aiding in better organization.
- This provides a foundation for a significantly more intelligent and efficient task input experience.

**Key Files:**
- `backend/src/prompts/aiPrompts.yaml` (specifically the `processQuickAdd` prompt)
- `backend/src/controllers/aiController.ts` (specifically the `processQuickAdd` function)

**Lessons Learned:**
- Iterative prompt engineering is key to achieving desired AI behavior, especially for complex parsing tasks.
- The structure of the AI's expected JSON output directly influences the complexity and capabilities of the backend processing logic.
- Designing prompts to extract granular, per-item details allows for more accurate and contextually relevant application of information.
- Separating AI parsing from backend business logic (e.g., AI suggests a location name, backend maps it to an ID and applies it) maintains a good separation of concerns.

---

## CSRF Protection Middleware Fix - Implemented 2025-05-11

**Problem:**
- A TypeScript build error (TS2554: Expected 1 arguments, but got 3) was occurring in `backend/src/routes/v2/auth.ts` related to the CSRF protection middleware.
- The custom `csrfProtectionMiddleware` was attempting to call the `validateRequest` function (from `csrf-csrf`) directly with `(req, res, next)`, which was incompatible with how TypeScript was interpreting its signature.

**Solution:**
1.  **Standard Library Usage:**
    *   The `doubleCsrf` initialization now also destructures `doubleCsrfProtection`, which is the library's standard, comprehensive middleware for both generating and validating CSRF tokens.
    *   The custom `csrfProtectionMiddleware` was renamed to `conditionalCsrfProtection`.
    *   Instead of calling `validateRequest(req, res, next)`, the `conditionalCsrfProtection` middleware now calls `return doubleCsrfProtection(req, res, next);` when CSRF protection is active.

**Outcome:**
- The TypeScript build error in `auth.ts` is resolved.
- CSRF protection is applied using the standard and recommended middleware from the `csrf-csrf` library, ensuring robust protection for relevant routes (e.g., `/logout`).
- Conditional application (e.g., bypassing in test mode if configured) is maintained.

**Key Files:**
- `backend/src/routes/v2/auth.ts`

**Lessons Learned:**
- When using third-party middleware libraries, it's generally best to use their primary exported middleware functions as intended by the library authors, rather than trying to call internal components directly, to avoid signature mismatches or unexpected behavior.
- TypeScript errors related to argument counts often point to a misunderstanding of a function's signature or how it's meant to be invoked.

---

## June 2024
- Pattern: Always align Kotlin, AGP, and Firebase plugin versions to avoid build errors in Flutter mobile projects.
