// Mocks generated by <PERSON><PERSON><PERSON> 5.4.5 from annotations
// in flashtasks_mobile/test/api_client_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i3;

import 'package:flashtasks_mobile/src/core/storage/secure_storage.dart' as _i2;
import 'package:mockito/mockito.dart' as _i1;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

/// A class which mocks [SecureStorageService].
///
/// See the documentation for <PERSON><PERSON><PERSON>'s code generation for more information.
class MockSecureStorageService extends _i1.Mock
    implements _i2.SecureStorageService {
  MockSecureStorageService() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i3.Future<void> write({required String? key, required String? value}) =>
      (super.noSuchMethod(
            Invocation.method(#write, [], {#key: key, #value: value}),
            returnValue: _i3.Future<void>.value(),
            returnValueForMissingStub: _i3.Future<void>.value(),
          )
          as _i3.Future<void>);

  @override
  _i3.Future<String?> read({required String? key}) =>
      (super.noSuchMethod(
            Invocation.method(#read, [], {#key: key}),
            returnValue: _i3.Future<String?>.value(),
          )
          as _i3.Future<String?>);

  @override
  _i3.Future<void> delete({required String? key}) =>
      (super.noSuchMethod(
            Invocation.method(#delete, [], {#key: key}),
            returnValue: _i3.Future<void>.value(),
            returnValueForMissingStub: _i3.Future<void>.value(),
          )
          as _i3.Future<void>);

  @override
  _i3.Future<void> deleteAll() =>
      (super.noSuchMethod(
            Invocation.method(#deleteAll, []),
            returnValue: _i3.Future<void>.value(),
            returnValueForMissingStub: _i3.Future<void>.value(),
          )
          as _i3.Future<void>);

  @override
  _i3.Future<void> storeAuthTokens({
    required String? accessToken,
    required String? refreshToken,
    required String? userId,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#storeAuthTokens, [], {
              #accessToken: accessToken,
              #refreshToken: refreshToken,
              #userId: userId,
            }),
            returnValue: _i3.Future<void>.value(),
            returnValueForMissingStub: _i3.Future<void>.value(),
          )
          as _i3.Future<void>);

  @override
  _i3.Future<String?> getAccessToken() =>
      (super.noSuchMethod(
            Invocation.method(#getAccessToken, []),
            returnValue: _i3.Future<String?>.value(),
          )
          as _i3.Future<String?>);

  @override
  _i3.Future<String?> getRefreshToken() =>
      (super.noSuchMethod(
            Invocation.method(#getRefreshToken, []),
            returnValue: _i3.Future<String?>.value(),
          )
          as _i3.Future<String?>);

  @override
  _i3.Future<String?> getUserId() =>
      (super.noSuchMethod(
            Invocation.method(#getUserId, []),
            returnValue: _i3.Future<String?>.value(),
          )
          as _i3.Future<String?>);

  @override
  _i3.Future<void> clearAuthTokens() =>
      (super.noSuchMethod(
            Invocation.method(#clearAuthTokens, []),
            returnValue: _i3.Future<void>.value(),
            returnValueForMissingStub: _i3.Future<void>.value(),
          )
          as _i3.Future<void>);

  @override
  _i3.Future<bool> hasAccessToken() =>
      (super.noSuchMethod(
            Invocation.method(#hasAccessToken, []),
            returnValue: _i3.Future<bool>.value(false),
          )
          as _i3.Future<bool>);
}
