"use client"

import React, { useEffect, useState, use<PERSON><PERSON>back, useMemo } from "react";
import { useRouter, usePathname } from "next/navigation";
import { useAuth } from "@/lib/auth-context";
import { AiChatbot } from "@/components/ai-chatbot";
import CategorySidebar from "@/components/category-sidebar";
import { DashboardProvider, useDashboardContext } from "@/lib/dashboard-context";
import { GroceryProvider } from "@/lib/grocery-context";
import { TaskFilters } from "@/app/dashboard/page";
import { TopNavigationBar } from "@/components/top-navigation-bar";
import { NewTaskDialog } from "@/components/new-task-dialog";

interface CalendarLayoutProps {
  children: React.ReactNode;
}

// Define special IDs used for context views (must match context/sidebar)
const COMPLETED_VIEW_CONTEXT_ID = 'completed_tasks_view';
const UNCATEGORIZED_VIEW_CONTEXT_ID = 'uncategorized_tasks_view';

// Inner component to access context and render layout
function CalendarLayoutContent({ children }: CalendarLayoutProps) {
  const { isAuthenticated, isLoading, user } = useAuth();
  const router = useRouter();
  const pathname = usePathname();
  
  // Enhanced authentication check
  useEffect(() => {
    if (!isLoading && !isAuthenticated) {
      router.push('/login');
    }
  }, [isAuthenticated, isLoading, router]);

  // Consume context values needed for the sidebar
  const {
    categories,
    isLoadingCategories,
    errorCategories,
    completedTaskCount,
    uncategorizedTaskCount,
    filters,
    setFilters,
    triggerRefresh,
    handleTaskAddedOrUpdated,
    handleShowUncategorized
  } = useDashboardContext();

  // Filter Handler
  const handleFilterChange = useCallback((newFilters: Partial<TaskFilters>) => {
    setFilters(prevFilters => ({
      ...prevFilters,
      ...newFilters,
      // Clear multi-select IDs when a context is set
      ...(newFilters.categoryContextId && { selectedCategoryIds: undefined })
    }));
  }, [setFilters]);

  // Handler specifically for showing completed tasks
  const handleShowCompleted = useCallback(() => {
    handleFilterChange({ categoryContextId: COMPLETED_VIEW_CONTEXT_ID, status: 'completed' });
  }, [handleFilterChange]);

  // Handler for showing all incomplete tasks (default view)
  const handleShowAllIncomplete = useCallback(() => {
    handleFilterChange({ categoryContextId: null, status: 'incomplete' });
  }, [handleFilterChange]);

  // Determine active context ID for the sidebar
  const currentActiveContextId = useMemo(() => {
    if (filters.categoryContextId === COMPLETED_VIEW_CONTEXT_ID) return COMPLETED_VIEW_CONTEXT_ID;
    if (filters.categoryContextId === UNCATEGORIZED_VIEW_CONTEXT_ID) return UNCATEGORIZED_VIEW_CONTEXT_ID;
    if (pathname === '/calendar') return null; // Calendar view
    return filters.categoryContextId ?? null;
  }, [filters.categoryContextId, pathname]);

  if (isLoading || !isAuthenticated) {
    return (
      <div className="flex h-screen items-center justify-center">
        <div className="text-lg">Loading...</div>
      </div>
    );
  }

  return (
    <div className="flex flex-col min-h-screen bg-background">
      <TopNavigationBar />
      <div className="flex flex-1 pt-16">
        <CategorySidebar
          categories={categories}
          isLoading={isLoadingCategories}
          error={errorCategories}
          activeContextId={currentActiveContextId}
          completedTaskCount={completedTaskCount}
          uncategorizedTaskCount={uncategorizedTaskCount}
          onCategorySelect={(categoryId) => handleFilterChange({ categoryContextId: categoryId, status: 'incomplete' })}
          onShowAll={handleShowAllIncomplete}
          onShowCompleted={handleShowCompleted}
          onShowUncategorized={handleShowUncategorized}
          onTaskAdded={handleTaskAddedOrUpdated}
          onCategoryUpdated={triggerRefresh}
          className="w-64 z-30"
        />
        <div className="flex-1 flex flex-col ml-64 overflow-hidden">
          {children}
          <AiChatbot />
        </div>
      </div>
      <NewTaskDialog onTaskAdded={handleTaskAddedOrUpdated} />
    </div>
  );
}

// Wrap the layout content with the provider
export default function CalendarLayout({ children }: CalendarLayoutProps) {
  return (
    <DashboardProvider>
      <GroceryProvider>
        <CalendarLayoutContent>
          {children}
        </CalendarLayoutContent>
      </GroceryProvider>
    </DashboardProvider>
  );
}
