import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../api/api_client.dart';
import '../config/api_config.dart';
import '../../features/groceries/models/grocery_item.dart';
import '../../features/groceries/models/grocery_insights.dart';
import '../../features/groceries/models/grocery_list.dart';
import '../../features/groceries/models/grocery_invitation.dart';

/// Service for interacting with the grocery API endpoints
class GroceryService {
  final ApiClient _apiClient;

  GroceryService({required ApiClient apiClient}) : _apiClient = apiClient;

  /// Get all grocery items for the user
  /// [listOwnerId] - Optional parameter to get items from a shared list
  Future<List<GroceryItem>> getGroceryItems({String? listOwnerId}) async {
    try {
      print('🔄 Fetching grocery items${listOwnerId != null ? ' for shared list owner: $listOwnerId' : ''}');
      final params = listOwnerId != null ? {'listOwnerId': listOwnerId} : <String, dynamic>{};
      final response = await _apiClient.get(ApiConfig.groceriesEndpoint, queryParameters: params);

      print('📊 Response status code: [33m[1m[4m[7m${response.statusCode}[0m');
      print('📊 Response data type: ${response.data?.runtimeType}');

      if (response.statusCode == 200 && response.data != null) {
        if (response.data is Map && response.data['data'] is List) {
          final items = (response.data['data'] as List)
              .map((item) => GroceryItem.fromJson(item as Map<String, dynamic>))
              .toList();
          print('📊 Parsed [32m${items.length}[0m grocery items');
          return items;
        }
      }

      throw Exception('Failed to fetch grocery items: Unexpected response format');
    } catch (e) {
      print('❌ Error fetching grocery items: $e');
      throw Exception('Failed to fetch grocery items: $e');
    }
  }

  /// Add one or more grocery items
  /// [listOwnerId] - Optional parameter to add items to a shared list
  Future<List<GroceryItem>> addGroceryItems(List<Map<String, dynamic>> items, {String? listOwnerId}) async {
    try {
      print('🔄 Adding grocery items: $items');

      // Validate items
      if (items.isEmpty) {
        throw Exception('No items provided');
      }

      // Ensure each item has a name
      for (final item in items) {
        if (item['name'] == null || (item['name'] as String).trim().isEmpty) {
          throw Exception('Each grocery item must have a non-empty name');
        }
      }

      final params = listOwnerId != null ? {'listOwnerId': listOwnerId} : <String, dynamic>{};
      final response = await _apiClient.post(
        ApiConfig.groceriesEndpoint,
        data: {'items': items},
        queryParameters: params,
      );

      print('📊 Response status code: ${response.statusCode}');
      print('📊 Response data type: ${response.data?.runtimeType}');

      if (response.statusCode == 201 && response.data != null) {
        // New: Handle { data: { items: [...] } }
        if (response.data is Map && response.data['data'] is Map && response.data['data']['items'] is List) {
          final items = (response.data['data']['items'] as List)
              .map((item) => GroceryItem.fromJson(item as Map<String, dynamic>))
              .toList();
          print('📊 Added ${items.length} grocery items');
          return items;
        }
        // Fix: Extract the 'data' field from the response map
        if (response.data is Map && response.data['data'] is List) {
          final items = (response.data['data'] as List)
              .map((item) => GroceryItem.fromJson(item as Map<String, dynamic>))
              .toList();
          print('📊 Added ${items.length} grocery items');
          return items;
        }
        // Handle response with items property (legacy)
        else if (response.data is Map && response.data['items'] is List) {
          final items = (response.data['items'] as List)
              .map((item) => GroceryItem.fromJson(item as Map<String, dynamic>))
              .toList();
          print('📊 Added ${items.length} grocery items');
          return items;
        }
        // Handle direct array response (legacy)
        else if (response.data is List) {
          final items = (response.data as List)
              .map((item) => GroceryItem.fromJson(item as Map<String, dynamic>))
              .toList();
          print('📊 Added ${items.length} grocery items');
          return items;
        }
      }

      throw Exception('Failed to add grocery items: Unexpected response format');
    } catch (e) {
      print('❌ Error adding grocery items: $e');
      throw Exception('Failed to add grocery items: $e');
    }
  }

  /// Update a grocery item
  Future<GroceryItem> updateGroceryItem(String id, Map<String, dynamic> updates) async {
    try {
      // Ensure category is properly formatted for API v2
      if (updates.containsKey('category') && updates['category'] is Map) {
        // Extract just the name for API v2
        updates['category'] = updates['category']['name'];
      }

      print('🔄 Updating grocery item $id with: $updates');
      final response = await _apiClient.put(
        ApiConfig.groceryItemByIdEndpoint(id),
        data: updates,
      );

      print('📊 Response status code: ${response.statusCode}');
      print('📊 Response data type: ${response.data?.runtimeType}');

      if (response.statusCode == 200 && response.data != null) {
        // Fix: Extract the 'data' field from the response map
        if (response.data is Map && response.data['data'] is Map) {
          final updatedItem = GroceryItem.fromJson(response.data['data'] as Map<String, dynamic>);
          print('📊 Updated grocery item: ${updatedItem.name}');
          return updatedItem;
        }
        // Legacy: direct object
        else if (response.data is Map) {
          final updatedItem = GroceryItem.fromJson(response.data as Map<String, dynamic>);
          print('📊 Updated grocery item: ${updatedItem.name}');
          return updatedItem;
        }
      }

      throw Exception('Failed to update grocery item: Unexpected response format');
    } catch (e) {
      print('❌ Error updating grocery item: $e');
      throw Exception('Failed to update grocery item: $e');
    }
  }

  /// Delete a grocery item
  Future<void> deleteGroceryItem(String id) async {
    try {
      print('🔄 Deleting grocery item $id');
      final response = await _apiClient.delete(
        ApiConfig.groceryItemByIdEndpoint(id),
      );

      print('📊 Response status code: ${response.statusCode}');

      if (response.statusCode != 200 && response.statusCode != 204) {
        throw Exception('Failed to delete grocery item: Server returned ${response.statusCode}');
      }

      print('✅ Grocery item deleted successfully');
    } catch (e) {
      print('❌ Error deleting grocery item: $e');
      throw Exception('Failed to delete grocery item: $e');
    }
  }

  /// Delete all checked grocery items
  Future<int> deleteCheckedGroceryItems() async {
    try {
      print('🔄 Deleting checked grocery items');
      final response = await _apiClient.delete(
        ApiConfig.groceryDeleteCheckedEndpoint,
      );

      print('📊 Response status code: ${response.statusCode}');
      print('📊 Response data: ${response.data}');

      if (response.statusCode == 200 && response.data != null) {
        // Fix: Extract the 'data' field from the response map
        if (response.data is Map && response.data['data'] is Map && response.data['data']['deletedCount'] != null) {
          final deletedCount = response.data['data']['deletedCount'] as int;
          print('✅ Deleted $deletedCount checked grocery items');
          return deletedCount;
        }
        // Legacy: direct deletedCount
        else if (response.data is Map && response.data['deletedCount'] != null) {
          final deletedCount = response.data['deletedCount'] as int;
          print('✅ Deleted $deletedCount checked grocery items');
          return deletedCount;
        }
      }

      throw Exception('Failed to delete checked grocery items: Unexpected response format');
    } catch (e) {
      print('❌ Error deleting checked grocery items: $e');
      throw Exception('Failed to delete checked grocery items: $e');
    }
  }

  /// Get grocery suggestions
  Future<List<String>> getGrocerySuggestions({int limit = 5}) async {
    try {
      print('🔄 Fetching grocery suggestions with limit $limit');
      final response = await _apiClient.get(
        ApiConfig.grocerySuggestionsEndpoint,
        queryParameters: {'limit': limit},
      );

      print('📊 Response status code: ${response.statusCode}');
      print('📊 Response data type: ${response.data?.runtimeType}');

      if (response.statusCode == 200 && response.data != null) {
        // Fix: Extract the 'data' field from the response map
        if (response.data is Map && response.data['data'] is List) {
          final suggestions = (response.data['data'] as List)
              .map((item) => item.toString())
              .toList();
          print('📊 Fetched ${suggestions.length} grocery suggestions');
          return suggestions;
        }
        // Legacy: direct list
        else if (response.data is List) {
          final suggestions = (response.data as List)
              .map((item) => item.toString())
              .toList();
          print('📊 Fetched ${suggestions.length} grocery suggestions');
          return suggestions;
        }
      }

      // Return empty list if no suggestions or unexpected format
      return [];
    } catch (e) {
      print('❌ Error fetching grocery suggestions: $e');
      // Return empty list on error to avoid crashing the UI
      return [];
    }
  }

  /// Get grocery insights
  Future<GroceryInsights?> getGroceryInsights() async {
    try {
      print('🔄 Fetching grocery insights');
      final response = await _apiClient.get(
        ApiConfig.groceryInsightsEndpoint,
      );

      print('📊 Response status code: ${response.statusCode}');
      print('📊 Response data type: ${response.data?.runtimeType}');

      if (response.statusCode == 200 && response.data != null) {
        // Fix: Extract the 'data' field from the response map
        if (response.data is Map && response.data['data'] is Map) {
          final insights = GroceryInsights.fromJson(response.data['data'] as Map<String, dynamic>);
          print('📊 Fetched grocery insights');
          return insights;
        }
        // Legacy: direct map
        else if (response.data is Map) {
          final insights = GroceryInsights.fromJson(response.data as Map<String, dynamic>);
          print('📊 Fetched grocery insights');
          return insights;
        }
      }

      return null;
    } catch (e) {
      print('❌ Error fetching grocery insights: $e');
      return null;
    }
  }
  /// Update categories for uncategorized items
  Future<Map<String, int>> updateCategories() async {
    try {
      print('🔄 Updating grocery categories');
      final response = await _apiClient.put(
        ApiConfig.groceryUpdateCategoriesEndpoint,
        data: {}, // Send empty object as data to avoid 404 error
      );

      print('📊 Response status code: ${response.statusCode}');
      print('📊 Response data: ${response.data}');

      if (response.statusCode == 200 && response.data != null) {
        if (response.data is Map) {
          final updated = response.data['updated'] as int? ?? 0;
          final total = response.data['total'] as int? ?? 0;
          print('✅ Updated $updated of $total grocery categories');
          return {'updated': updated, 'total': total};
        }
      }

      throw Exception('Failed to update grocery categories: Unexpected response format');
    } catch (e) {
      print('❌ Error updating grocery categories: $e');
      throw Exception('Failed to update grocery categories: $e');
    }
  }

  /// Search grocery library
  Future<List<String>> searchGroceryLibrary(String query, {int limit = 5}) async {
    try {
      if (query.trim().isEmpty) {
        return [];
      }

      print('🔄 Searching grocery library for "$query" with limit $limit');
      final response = await _apiClient.get(
        ApiConfig.groceryLibrarySearchEndpoint,
        queryParameters: {'q': query, 'limit': limit},
      );

      print('📊 Response status code: ${response.statusCode}');
      print('📊 Response data type: ${response.data?.runtimeType}');

      if (response.statusCode == 200 && response.data != null) {
        // Fix: Extract the 'data' field from the response map
        if (response.data is Map && response.data['data'] is List) {
          final results = (response.data['data'] as List)
              .map((item) => item.toString())
              .toList();
          print('📊 Found ${results.length} grocery library matches');
          return results;
        }
        // Legacy: direct list
        else if (response.data is List) {
          final results = (response.data as List)
              .map((item) => item.toString())
              .toList();
          print('📊 Found ${results.length} grocery library matches');
          return results;
        }
      }

      return [];
    } catch (e) {
      print('❌ Error searching grocery library: $e');
      return [];
    }
  }

  // ===== COLLABORATION METHODS =====

  /// Get grocery list metadata
  Future<GroceryList> getGroceryList() async {
    try {
      print('🔄 Fetching grocery list metadata');
      final response = await _apiClient.get(ApiConfig.groceryListEndpoint);

      print('📊 Response status code: ${response.statusCode}');
      print('📊 Response data type: ${response.data?.runtimeType}');

      if (response.statusCode == 200 && response.data != null) {
        if (response.data is Map && response.data['data'] is Map) {
          final groceryList = GroceryList.fromJson(response.data['data'] as Map<String, dynamic>);
          print('📊 Fetched grocery list metadata');
          return groceryList;
        }
      }

      throw Exception('Failed to fetch grocery list: Unexpected response format');
    } catch (e) {
      print('❌ Error fetching grocery list: $e');
      throw Exception('Failed to fetch grocery list: $e');
    }
  }

  /// Enable sharing for grocery list
  Future<GroceryList> enableSharing(GroceryShareSettings shareSettings) async {
    try {
      print('🔄 Enabling grocery list sharing');
      final response = await _apiClient.post(
        ApiConfig.groceryListSharingEndpoint,
        data: {'shareSettings': shareSettings.toJson()},
      );

      print('📊 Response status code: ${response.statusCode}');

      if (response.statusCode == 200 && response.data != null) {
        if (response.data is Map && response.data['data'] is Map) {
          final groceryList = GroceryList.fromJson(response.data['data'] as Map<String, dynamic>);
          print('✅ Grocery list sharing enabled');
          return groceryList;
        }
      }

      throw Exception('Failed to enable sharing: Unexpected response format');
    } catch (e) {
      print('❌ Error enabling sharing: $e');
      throw Exception('Failed to enable sharing: $e');
    }
  }

  /// Disable sharing for grocery list
  Future<GroceryList> disableSharing() async {
    try {
      print('🔄 Disabling grocery list sharing');
      final response = await _apiClient.post(ApiConfig.groceryListUnsharingEndpoint);

      print('📊 Response status code: ${response.statusCode}');

      if (response.statusCode == 200 && response.data != null) {
        if (response.data is Map && response.data['data'] is Map) {
          final groceryList = GroceryList.fromJson(response.data['data'] as Map<String, dynamic>);
          print('✅ Grocery list sharing disabled');
          return groceryList;
        }
      }

      throw Exception('Failed to disable sharing: Unexpected response format');
    } catch (e) {
      print('❌ Error disabling sharing: $e');
      throw Exception('Failed to disable sharing: $e');
    }
  }

  /// Update share settings
  Future<GroceryList> updateShareSettings(GroceryShareSettings shareSettings) async {
    try {
      print('🔄 Updating grocery list share settings');
      final response = await _apiClient.put(
        ApiConfig.groceryShareSettingsEndpoint,
        data: {'shareSettings': shareSettings.toJson()},
      );

      print('📊 Response status code: ${response.statusCode}');

      if (response.statusCode == 200 && response.data != null) {
        if (response.data is Map && response.data['data'] is Map) {
          final groceryList = GroceryList.fromJson(response.data['data'] as Map<String, dynamic>);
          print('✅ Share settings updated');
          return groceryList;
        }
      }

      throw Exception('Failed to update share settings: Unexpected response format');
    } catch (e) {
      print('❌ Error updating share settings: $e');
      throw Exception('Failed to update share settings: $e');
    }
  }

  /// Get shared grocery lists where user is a collaborator
  Future<List<GroceryList>> getSharedLists() async {
    try {
      print('🔄 Fetching shared grocery lists');
      final response = await _apiClient.get(ApiConfig.sharedGroceryListsEndpoint);

      print('📊 Response status code: ${response.statusCode}');
      print('📊 Response data type: ${response.data?.runtimeType}');

      if (response.statusCode == 200 && response.data != null) {
        if (response.data is Map && response.data['data'] is List) {
          final sharedLists = (response.data['data'] as List)
              .map((item) => GroceryList.fromJson(item as Map<String, dynamic>))
              .toList();
          print('📊 Fetched ${sharedLists.length} shared grocery lists');
          for (final list in sharedLists) {
            print('📊 Shared list: ${list.name ?? "Unnamed"} (Owner: ${list.userId}, Role: ${list.userRole?.name ?? "Unknown"}, ID: ${list.id})');
          }
          print('📊 Shared lists parsed successfully!');
          return sharedLists;
        } else {
          print('📊 Unexpected response structure: ${response.data}');
        }
      } else {
        print('📊 Unexpected response status: ${response.statusCode}');
      }

      throw Exception('Failed to fetch shared lists: Unexpected response format');
    } catch (e) {
      print('❌ Error fetching shared lists: $e');
      throw Exception('Failed to fetch shared lists: $e');
    }
  }

  /// Get pending invitations for the current user
  Future<List<GroceryListInvitation>> getPendingInvitations() async {
    try {
      print('🔄 Fetching pending grocery list invitations');
      final response = await _apiClient.get(ApiConfig.groceryInvitationsEndpoint);

      print('📊 Response status code: ${response.statusCode}');
      print('📊 Response data type: ${response.data?.runtimeType}');

      if (response.statusCode == 200 && response.data != null) {
        if (response.data is Map && response.data['data'] is List) {
          final invitations = (response.data['data'] as List)
              .map((item) => GroceryListInvitation.fromJson(item as Map<String, dynamic>))
              .toList();
          print('📊 Fetched ${invitations.length} pending invitations');
          return invitations;
        }
      }

      throw Exception('Failed to fetch invitations: Unexpected response format');
    } catch (e) {
      print('❌ Error fetching invitations: $e');
      throw Exception('Failed to fetch invitations: $e');
    }
  }

  /// Accept an invitation
  Future<void> acceptInvitation(String token) async {
    try {
      print('🔄 Accepting grocery list invitation');
      final response = await _apiClient.post(ApiConfig.groceryInvitationAcceptEndpoint(token));

      print('📊 Response status code: ${response.statusCode}');

      if (response.statusCode != 200) {
        throw Exception('Failed to accept invitation: Server returned ${response.statusCode}');
      }

      print('✅ Invitation accepted successfully');
    } catch (e) {
      print('❌ Error accepting invitation: $e');
      throw Exception('Failed to accept invitation: $e');
    }
  }

  /// Decline an invitation
  Future<void> declineInvitation(String token) async {
    try {
      print('🔄 Declining grocery list invitation');
      final response = await _apiClient.post(ApiConfig.groceryInvitationDeclineEndpoint(token));

      print('📊 Response status code: ${response.statusCode}');

      if (response.statusCode != 200) {
        throw Exception('Failed to decline invitation: Server returned ${response.statusCode}');
      }

      print('✅ Invitation declined successfully');
    } catch (e) {
      print('❌ Error declining invitation: $e');
      throw Exception('Failed to decline invitation: $e');
    }
  }

  /// Add collaborator to grocery list
  Future<GroceryList> addCollaborator(String email, CollaboratorRole role) async {
    try {
      print('🔄 Adding collaborator to grocery list');
      final response = await _apiClient.post(
        ApiConfig.groceryCollaboratorsEndpoint,
        data: {'email': email, 'role': role.name},
      );

      print('📊 Response status code: ${response.statusCode}');

      if (response.statusCode == 200 && response.data != null) {
        if (response.data is Map && response.data['data'] is Map && response.data['data']['list'] is Map) {
          final groceryList = GroceryList.fromJson(response.data['data']['list'] as Map<String, dynamic>);
          print('✅ Collaborator added successfully');
          return groceryList;
        }
      }

      throw Exception('Failed to add collaborator: Unexpected response format');
    } catch (e) {
      print('❌ Error adding collaborator: $e');
      throw Exception('Failed to add collaborator: $e');
    }
  }

  /// Remove collaborator from grocery list
  Future<GroceryList> removeCollaborator(String collaboratorId) async {
    try {
      print('🔄 Removing collaborator from grocery list');
      final response = await _apiClient.delete(ApiConfig.groceryCollaboratorRemoveEndpoint(collaboratorId));

      print('📊 Response status code: ${response.statusCode}');

      if (response.statusCode == 200 && response.data != null) {
        if (response.data is Map && response.data['data'] is Map) {
          final groceryList = GroceryList.fromJson(response.data['data'] as Map<String, dynamic>);
          print('✅ Collaborator removed successfully');
          return groceryList;
        }
      }

      throw Exception('Failed to remove collaborator: Unexpected response format');
    } catch (e) {
      print('❌ Error removing collaborator: $e');
      throw Exception('Failed to remove collaborator: $e');
    }
  }
}

/// Provider for the grocery service
final groceryServiceProvider = Provider<GroceryService>((ref) {
  final apiClient = ref.watch(apiClientProvider);
  return GroceryService(apiClient: apiClient);
});
