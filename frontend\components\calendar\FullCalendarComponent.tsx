'use client';

import { useEffect, useRef, useState, useCallback } from 'react';
import FullCalendar from '@fullcalendar/react';
import dayGridPlugin from '@fullcalendar/daygrid';
import timeGridPlugin from '@fullcalendar/timegrid';
import interactionPlugin from '@fullcalendar/interaction';
import listPlugin from '@fullcalendar/list';
import { format, addHours, isBefore } from 'date-fns'; // Date utility functions
import { z } from 'zod';
import { Button } from '@/components/ui/button';
import { Plus, Calendar as CalendarIcon, List, Grid, Check, Clock, AlertTriangle, PanelLeft } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { taskService } from '@/lib/task-service';
import type { TaskModel, TaskPriority, TaskFilters } from '@/lib/types/task.model';
import { useToast } from '@/components/ui/use-toast';
import { CalendarEvent, mapTaskToCalendarEvent } from '@/lib/calendar-utils'; // Calendar utilities
import { NewTaskDialog, type TaskFormValues } from '../new-task-dialog';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { cn } from '@/lib/utils';
import { CalendarSidebar } from './CalendarSidebar';
import { Sheet, SheetContent, SheetTrigger } from '@/components/ui/sheet';

type CalendarView = 'dayGridMonth' | 'timeGridWeek' | 'timeGridDay' | 'listWeek';

const PRIORITY_COLORS = {
  Low: 'bg-blue-100 text-blue-800',
  Medium: 'bg-green-100 text-green-800',
  High: 'bg-yellow-100 text-yellow-800',
  Critical: 'bg-red-100 text-red-800',
};

interface ExtendedTaskFormValues extends Omit<TaskFormValues, 'id' | 'priority'> {
  id?: string;
  priority: 'low' | 'medium' | 'high' | 'critical';
  description?: string;
  locationId?: string | null;
}

export default function FullCalendarComponent() {
  const router = useRouter();
  const { toast } = useToast();
  const calendarRef = useRef<FullCalendar>(null);
  // Track current view for UI state
  const [currentView, setCurrentView] = useState<CalendarView>('dayGridMonth');
  const [events, setEvents] = useState<CalendarEvent[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  // Keep selectedEvent state for potential future use
  const [selectedEvent, setSelectedEvent] = useState<CalendarEvent | null>(null);
  const [formData, setFormData] = useState<ExtendedTaskFormValues>({
    title: '',
    description: '',
    dueDate: new Date(),
    dueTime: format(new Date(), 'HH:mm'),
    priority: 'medium' as const,
    categories: [],
    reminders: [],
    useAI: true
  });
  const [isNewTaskDialogOpen, setIsNewTaskDialogOpen] = useState(false);
  const [selectedDate, setSelectedDate] = useState<Date>(new Date());
  const [selectedCategories, setSelectedCategories] = useState<string[]>([]);
  const [categories, setCategories] = useState<{id: string; name: string; color: string}[]>([]);
  const [isLoadingCategories, setIsLoadingCategories] = useState(false);
  const [isMobile, setIsMobile] = useState(false);
  const [sidebarOpen, setSidebarOpen] = useState(true);

  const fetchTasks = useCallback(async () => {
    try {
      setIsLoading(true);
      
      // Set up filters to get all tasks
      const filters: TaskFilters = {
        status: 'all',
      };
      
      // Fetch tasks from the API
      const response = await taskService.getTasks(filters);
      
      // Map tasks to calendar events
      const calendarEvents = response.data.map((task: TaskModel) => {
        // Use deadline for due date if available, otherwise use current date
        const dueDate = task.deadline ? new Date(task.deadline) : new Date();
        
        // Map task priority to lowercase for consistency
        const priority = task.priority ? task.priority.toLowerCase() as 'low' | 'medium' | 'high' | 'critical' : 'medium';
        
        // Handle location - it can be a string, LocationModel, or undefined
        let location: string | { name: string } | undefined = undefined;
        if (task.location) {
          location = typeof task.location === 'string' 
            ? task.location 
            : { name: task.location.name || 'Location' };
        }
        
        // Create base event properties
        const baseEvent: Omit<CalendarEvent, 'extendedProps'> & { extendedProps: any } = {
          id: task._id,
          title: task.title,
          start: dueDate,
          end: addHours(new Date(dueDate), 1),
          allDay: true,
          extendedProps: {
            type: 'task' as const,
            taskId: task._id,
            completed: task.completed || false,
            priority: task.priority || 'Medium',
            description: task.content || '',
            location,
            // Add className for styling
            className: `priority-${priority}`
          },
        };
        
        // Type assertion to CalendarEvent
        const event = baseEvent as unknown as CalendarEvent;
        
        // Store additional task data in a separate object for reference
        // This can be accessed via the event's ID in the events array
        const taskData = {
          id: task._id,
          categories: task.categories || [],
          reminders: task.reminders || [],
          content: task.content || '',
          useAI: true,
        };
        
        // Store the task data in a way that can be accessed later
        // For example, using a ref or a separate state variable
        // This is a simplified example - you may need to adjust based on your needs
        
        return event;
      });
      
      setEvents(calendarEvents);
    } catch (error) {
      console.error('Error fetching tasks:', error);
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'Failed to load tasks. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  }, [toast]);

  // Check if mobile view
  useEffect(() => {
    const checkIfMobile = () => {
      setIsMobile(window.innerWidth < 768);
      if (window.innerWidth < 768) {
        setSidebarOpen(false);
      } else {
        setSidebarOpen(true);
      }
    };
    
    checkIfMobile();
    window.addEventListener('resize', checkIfMobile);
    return () => window.removeEventListener('resize', checkIfMobile);
  }, []);

  // Fetch categories
  useEffect(() => {
    const loadCategories = async () => {
      try {
        setIsLoadingCategories(true);
        // Replace with actual category service call
        // const response = await categoryService.getCategories();
        // setCategories(response.data);
      } catch (error) {
        console.error('Error loading categories:', error);
      } finally {
        setIsLoadingCategories(false);
      }
    };
    
    loadCategories();
  }, []);

  useEffect(() => {
    fetchTasks();
  }, [fetchTasks, selectedCategories]);
  
  const handleCategoryToggle = (categoryId: string) => {
    setSelectedCategories(prev => 
      prev.includes(categoryId)
        ? prev.filter(id => id !== categoryId)
        : [...prev, categoryId]
    );
  };
  
  const handleTaskClick = (task: any) => {
    // Handle task click in the upcoming list
    const calendarApi = calendarRef.current?.getApi();
    if (calendarApi) {
      calendarApi.gotoDate(new Date(task.deadline));
      // You might want to highlight or select the task in the calendar
    }
  };

  const handleDateSelect = (selectInfo: any) => {
    const start = selectInfo.start;
    const end = selectInfo.end || addHours(selectInfo.start, 1);
    
    // Set form data for new task with the selected date/time
    setFormData({
      title: '',
      description: '',
      dueDate: start,
      dueTime: format(start, 'HH:mm'),
      priority: 'medium' as const,
      categories: [],
      reminders: [],
      useAI: true
    });
    
    setSelectedEvent(null);
    setIsNewTaskDialogOpen(true);
    
    // Ensure the calendar view is updated
    const calendarApi = selectInfo.view.calendar;
    calendarApi.unselect(); // Clear date selection
  };

  const handleEventClick = (clickInfo: any) => {
    const event = clickInfo.event;
    const extendedProps = event.extendedProps || {};
    
    // Update the selected event for reference
    setSelectedEvent({
      id: event.id,
      title: event.title,
      start: event.start,
      end: event.end || event.start,
      allDay: event.allDay,
      extendedProps,
    });
    
    // Map the event data to the TaskFormValues format
    const priority = (extendedProps.priority || 'medium').toLowerCase() as 'low' | 'medium' | 'high' | 'critical';
    
    // Set form data with the event's details
    const formData: ExtendedTaskFormValues = {
      id: event.id, // Include the ID for updates
      title: event.title,
      description: extendedProps.content || extendedProps.description || '',
      dueDate: event.start,
      dueTime: format(event.start, 'HH:mm'),
      priority,
      categories: extendedProps.categories || [],
      reminders: extendedProps.reminders || [],
      locationId: extendedProps.locationId || null,
      useAI: extendedProps.useAI !== false // Default to true if not specified
    };
    
    setFormData(formData);
    
    // Open the dialog for editing
    setIsNewTaskDialogOpen(true);
    
    // Prevent default browser behavior
    clickInfo.jsEvent.preventDefault();
  };

  const renderEventContent = (eventInfo: any) => {
    const { event } = eventInfo;
    const { extendedProps } = event;
    const isTask = extendedProps?.type === 'task';
    const isCompleted = extendedProps?.completed;
    const priority = extendedProps?.priority || 'Medium';
    const isOverdue = event.end && isBefore(event.end, new Date()) && !isCompleted;

    return (
      <div className={cn(
        'p-1 overflow-hidden rounded border',
        isCompleted ? 'opacity-70' : '',
        isTask ? 'border-l-4' : 'border-l-2',
        isTask ? `border-l-${priority.toLowerCase()}-400` : 'border-l-blue-400',
        isOverdue && !isCompleted ? 'bg-red-50' : ''
      )}>
        <div className="flex items-center gap-1">
          {isTask && (
            <span className={cn(
              'inline-flex items-center justify-center w-4 h-4 rounded-full mr-1',
              isCompleted ? 'bg-green-100 text-green-800' : 'bg-blue-100 text-blue-800',
              'text-xs font-medium'
            )}>
              {isCompleted ? (
                <Check className="w-3 h-3" />
              ) : isOverdue ? (
                <AlertTriangle className="w-3 h-3" />
              ) : (
                <Clock className="w-3 h-3" />
              )}
            </span>
          )}
          <div className="flex-1 min-w-0">
            <div className="font-medium truncate">
              {isCompleted ? <s>{event.title}</s> : event.title}
            </div>
            {eventInfo.timeText && (
              <div className="text-xs opacity-80 flex items-center gap-1">
                {eventInfo.timeText}
                {priority !== 'Medium' && (
                  <span className={cn(
                    'inline-flex items-center px-1 rounded text-xs',
                    PRIORITY_COLORS[priority as keyof typeof PRIORITY_COLORS]
                  )}>
                    {priority}
                  </span>
                )}
              </div>
            )}
          </div>
        </div>
      </div>
    );
  };

  const changeView = (view: CalendarView) => {
    if (calendarRef.current) {
      const calendarApi = calendarRef.current.getApi();
      calendarApi.changeView(view);
      setCurrentView(view);
    }
  };

  const handleSaveTask = async (data: ExtendedTaskFormValues) => {
    if (!data.title.trim()) {
      toast({
        title: "Error",
        description: "Task title is required.",
        variant: "destructive",
      });
      return;
    }

    try {
      setIsLoading(true);
      
      // Combine date and time if both are provided
      let dueDate = data.dueDate ? new Date(data.dueDate) : new Date();
      
      if (data.dueTime) {
        const [hours, minutes] = data.dueTime.split(':').map(Number);
        dueDate.setHours(hours, minutes, 0, 0);
      }
      
      // Prepare task data for API
      const taskData: any = {
        title: data.title,
        content: data.description || '',
        deadline: dueDate.toISOString(),
        priority: data.priority.toUpperCase() as TaskPriority,
        categories: data.categories || [],
        reminders: data.reminders || [],
      };
      
      // Only add locationId if it exists
      if (data.locationId) {
        taskData.locationId = data.locationId;
      }
      
      // Add useAI flag if needed
      if (data.useAI !== undefined) {
        taskData.useAI = data.useAI;
      }
      
      let response;
      
      if (data.id) {
        // Update existing task
        response = await taskService.updateTask(data.id, taskData);
        toast({
          title: 'Task updated',
          description: 'Your task has been updated successfully.',
        });
      } else {
        // Create new task
        response = await taskService.createTask(taskData);
        toast({
          title: 'Task created',
          description: 'Your task has been created successfully.',
        });
      }
      
      // Refresh the task list and close the dialog
      await fetchTasks();
      setIsNewTaskDialogOpen(false);
      
      // Reset form data
      setFormData({
        title: '',
        description: '',
        dueDate: new Date(),
        dueTime: format(new Date(), 'HH:mm'),
        priority: 'medium',
        categories: [],
        reminders: [],
        useAI: true,
      });
      
      return response;
    } catch (error) {
      console.error('Error saving task:', error);
      const errorMessage = error instanceof Error ? error.message : 'Failed to save task. Please try again.';
      toast({
        title: 'Error',
        description: errorMessage,
        variant: 'destructive',
      });
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  if (isLoading && events.length === 0) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  return (
    <div className="flex flex-col h-full">
      {/* Mobile header with menu button */}
      <div className="md:hidden flex items-center justify-between p-2 border-b">
        <Sheet open={sidebarOpen} onOpenChange={setSidebarOpen}>
          <SheetTrigger asChild>
            <Button variant="ghost" size="icon">
              <PanelLeft className="h-5 w-5" />
              <span className="sr-only">Toggle sidebar</span>
            </Button>
          </SheetTrigger>
          <SheetContent side="left" className="p-0 w-[280px] sm:w-[300px]">
            <CalendarSidebar
              selectedDate={selectedDate}
              onDateSelect={(date) => {
                setSelectedDate(date);
                const calendarApi = calendarRef.current?.getApi();
                if (calendarApi) {
                  calendarApi.gotoDate(date);
                }
                if (isMobile) setSidebarOpen(false);
              }}
              tasks={[]} // Pass your tasks here
              categories={categories}
              selectedCategories={selectedCategories}
              onCategoryToggle={handleCategoryToggle}
              onTaskClick={handleTaskClick}
            />
          </SheetContent>
        </Sheet>
        <h1 className="text-xl font-semibold">
          {format(selectedDate, 'MMMM yyyy')}
        </h1>
        <div className="w-10"></div> {/* Spacer for flex layout */}
      </div>
      
      <div className="flex flex-1 overflow-hidden">
        {/* Desktop Sidebar */}
        <div className={`hidden md:block ${sidebarOpen ? 'w-80' : 'w-0'} transition-all duration-300 border-r`}>
          {sidebarOpen && (
            <CalendarSidebar
              selectedDate={selectedDate}
              onDateSelect={(date) => {
                setSelectedDate(date);
                const calendarApi = calendarRef.current?.getApi();
                if (calendarApi) {
                  calendarApi.gotoDate(date);
                }
              }}
              tasks={[]} // Pass your tasks here
              categories={categories}
              selectedCategories={selectedCategories}
              onCategoryToggle={handleCategoryToggle}
              onTaskClick={handleTaskClick}
            />
          )}
        </div>
        
        {/* Main Content */}
        <div className="flex-1 flex flex-col overflow-hidden">
      <div className="flex justify-between items-center mb-4">
        <div className="flex space-x-2">
          <Button
            variant={currentView === 'dayGridMonth' ? 'default' : 'outline'}
            size="sm"
            onClick={() => changeView('dayGridMonth')}
          >
            <Grid className="h-4 w-4 mr-2" />
            Month
          </Button>
          <Button
            variant={currentView === 'timeGridWeek' ? 'default' : 'outline'}
            size="sm"
            onClick={() => changeView('timeGridWeek')}
          >
            <CalendarIcon className="h-4 w-4 mr-2" />
            Week
          </Button>
          <Button
            variant={currentView === 'timeGridDay' ? 'default' : 'outline'}
            size="sm"
            onClick={() => changeView('timeGridDay')}
          >
            <CalendarIcon className="h-4 w-4 mr-2" />
            Day
          </Button>
          <Button
            variant={currentView === 'listWeek' ? 'default' : 'outline'}
            size="sm"
            onClick={() => changeView('listWeek')}
          >
            <List className="h-4 w-4 mr-2" />
            List
          </Button>
        </div>
        <Button
          onClick={() => {
            const now = new Date();
            
            // Initialize form data for a new task
            setFormData({
              title: '',
              description: '',
              dueDate: now,
              dueTime: format(now, 'HH:mm'),
              priority: 'medium' as const,
              categories: [],
              reminders: [],
              useAI: true
            });
            
            setSelectedEvent(null);
            setIsNewTaskDialogOpen(true);
            
            // Update calendar view
            const calendarApi = calendarRef.current?.getApi();
            if (calendarApi) {
              calendarApi.changeView('dayGridMonth');
              calendarApi.gotoDate(now);
            }
          }}
          size="sm"
        >
          <Plus className="h-4 w-4 mr-2" />
          New Task
        </Button>

        <Dialog open={isNewTaskDialogOpen} onOpenChange={setIsNewTaskDialogOpen}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Create New Task</DialogTitle>
              <DialogDescription>
                Add a new task to your calendar
              </DialogDescription>
            </DialogHeader>
            <NewTaskDialog
              onTaskAdded={() => {
                setIsNewTaskDialogOpen(false);
                fetchTasks();
              }}
            />
          </DialogContent>
        </Dialog>
      </div>

      <div className="flex-1">
        <FullCalendar
          ref={calendarRef}
          plugins={[dayGridPlugin, timeGridPlugin, interactionPlugin, listPlugin]}
          initialView="dayGridMonth"
          headerToolbar={false}
          height="auto"
          events={events}
          editable={true}
          selectable={true}
          selectMirror={true}
          dayMaxEvents={true}
          weekends={true}
          nowIndicator={true}
          initialDate={new Date()}
          select={handleDateSelect}
          eventClick={handleEventClick}
          eventContent={renderEventContent}
          eventClassNames="cursor-pointer hover:opacity-90"
          dayHeaderClassNames="bg-gray-50 dark:bg-gray-800"
          dayCellClassNames="hover:bg-gray-50 dark:hover:bg-gray-800"
          eventTimeFormat={{
            hour: '2-digit',
            minute: '2-digit',
            hour12: true
          }}
          views={{
            dayGridMonth: {
              dayMaxEventRows: 3,
            },
            timeGridWeek: {
              dayMaxEventRows: 4,
            },
            timeGridDay: {
              dayMaxEventRows: 6,
            },
            listWeek: {
              type: 'listWeek',
              duration: { weeks: 1 },
            },
          }}
        />
        </div>
      </div>
    </div>
  );
}
