'use client';

import { useEffect, useRef, useState, useCallback } from 'react';
import FullCalendar from '@fullcalendar/react';
import dayGridPlugin from '@fullcalendar/daygrid';
import timeGridPlugin from '@fullcalendar/timegrid';
import interactionPlugin from '@fullcalendar/interaction';
import listPlugin from '@fullcalendar/list';
import { format, addHours, isBefore } from 'date-fns'; // Date utility functions
import { z } from 'zod';
import { Button } from '@/components/ui/button';
import { Plus, Calendar as CalendarIcon, List, Grid, Check, Clock, AlertTriangle, PanelLeft } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { taskService } from '@/lib/task-service';
import type { TaskModel, TaskPriority, TaskFilters } from '@/lib/types/task.model';
import { useToast } from '@/components/ui/use-toast';
import { CalendarEvent, mapTaskToCalendarEvent } from '@/lib/calendar-utils'; // Calendar utilities
import { NewTaskDialog, type TaskFormValues } from '../new-task-dialog';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { cn } from '@/lib/utils';
import { CalendarSidebar } from './CalendarSidebar';
import { Sheet, SheetContent, SheetTrigger } from '@/components/ui/sheet';

type CalendarView = 'dayGridMonth' | 'timeGridWeek' | 'timeGridDay' | 'listWeek';

const PRIORITY_COLORS = {
  Low: 'bg-blue-100 text-blue-800',
  Medium: 'bg-green-100 text-green-800',
  High: 'bg-yellow-100 text-yellow-800',
  Critical: 'bg-red-100 text-red-800',
};

interface ExtendedTaskFormValues extends Omit<TaskFormValues, 'id' | 'priority'> {
  id?: string;
  priority: 'low' | 'medium' | 'high' | 'critical';
  description?: string;
  locationId?: string | null;
}

export default function FullCalendarComponent() {
  const router = useRouter();
  const { toast } = useToast();
  const calendarRef = useRef<FullCalendar>(null);
  // Track current view for UI state
  const [currentView, setCurrentView] = useState<CalendarView>('dayGridMonth');
  const [events, setEvents] = useState<CalendarEvent[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  // Keep selectedEvent state for potential future use
  const [selectedEvent, setSelectedEvent] = useState<CalendarEvent | null>(null);
  const [formData, setFormData] = useState<ExtendedTaskFormValues>({
    title: '',
    description: '',
    dueDate: new Date(),
    dueTime: format(new Date(), 'HH:mm'),
    priority: 'medium' as const,
    categories: [],
    reminders: [],
    useAI: true
  });
  const [isNewTaskDialogOpen, setIsNewTaskDialogOpen] = useState(false);
  const [selectedDate, setSelectedDate] = useState<Date>(new Date());
  const [selectedCategories, setSelectedCategories] = useState<string[]>([]);
  const [categories, setCategories] = useState<{id: string; name: string; color: string}[]>([]);
  const [isLoadingCategories, setIsLoadingCategories] = useState(false);
  const [isMobile, setIsMobile] = useState(false);
  const [sidebarOpen, setSidebarOpen] = useState(true);

  const fetchTasks = useCallback(async () => {
    try {
      setIsLoading(true);
      
      // Set up filters to get all tasks
      const filters: TaskFilters = {
        status: 'all',
      };
      
      // Fetch tasks from the API
      const response = await taskService.getTasks(filters);
      
      // Map tasks to calendar events
      const calendarEvents = response.data.map((task: TaskModel) => {
        // Use deadline for due date if available, otherwise use current date
        const dueDate = task.deadline ? new Date(task.deadline) : new Date();
        
        // Map task priority to lowercase for consistency
        const priority = task.priority ? task.priority.toLowerCase() as 'low' | 'medium' | 'high' | 'critical' : 'medium';
        
        // Handle location - it can be a string, LocationModel, or undefined
        let location: string | { name: string } | undefined = undefined;
        if (task.location) {
          location = typeof task.location === 'string' 
            ? task.location 
            : { name: task.location.name || 'Location' };
        }
        
        return mapTaskToCalendarEvent(task);
      });
      
      setEvents(calendarEvents);
    } catch (error) {
      console.error('Error fetching tasks:', error);
      toast({
        title: 'Error',
        description: 'Failed to load tasks. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  }, [toast]);

  const fetchCategories = useCallback(async () => {
    try {
      setIsLoadingCategories(true);
      const response = await taskService.getCategories();
      setCategories(response);
    } catch (error) {
      console.error('Error fetching categories:', error);
      toast({
        title: 'Error',
        description: 'Failed to load categories. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setIsLoadingCategories(false);
    }
  }, [toast]);

  // Fetch tasks and categories on component mount
  useEffect(() => {
    fetchTasks();
    fetchCategories();
    
    // Check if we're on mobile
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768);
      setSidebarOpen(window.innerWidth >= 768);
    };
    
    checkMobile();
    window.addEventListener('resize', checkMobile);
    
    return () => {
      window.removeEventListener('resize', checkMobile);
    };
  }, [fetchTasks, fetchCategories]);

  // Handle category toggle for filtering
  const handleCategoryToggle = (categoryId: string) => {
    setSelectedCategories(prev => {
      if (prev.includes(categoryId)) {
        return prev.filter(id => id !== categoryId);
      } else {
        return [...prev, categoryId];
      }
    });
  };

  // Change calendar view (month, week, day, list)
  const changeView = (view: CalendarView) => {
    setCurrentView(view);
    const calendarApi = calendarRef.current?.getApi();
    if (calendarApi) {
      calendarApi.changeView(view);
    }
  };

  // Handle task click
  const handleTaskClick = (task: TaskModel) => {
    // Find the corresponding event
    const event = events.find(e => e.id === task._id);
    if (event) {
      setSelectedEvent(event);
      setIsDialogOpen(true);
    }
  };

  // Handle event click in the calendar
  const handleEventClick = (info: any) => {
    const eventId = info.event.id;
    const event = events.find(e => e.id === eventId);
    if (event) {
      setSelectedEvent(event);
      setIsDialogOpen(true);
    }
  };

  // Handle date click in the calendar
  const handleDateClick = (info: any) => {
    const clickedDate = new Date(info.date);
    
    // Initialize form data for a new task
    setFormData({
      title: '',
      description: '',
      dueDate: clickedDate,
      dueTime: format(new Date(), 'HH:mm'),
      priority: 'medium' as const,
      categories: [],
      reminders: [],
      useAI: true
    });
    
    setSelectedEvent(null);
    setIsNewTaskDialogOpen(true);
  };

  // Handle date select in the calendar (for creating tasks with duration)
  const handleDateSelect = (info: any) => {
    const startDate = new Date(info.start);
    const endDate = new Date(info.end);
    
    // Initialize form data for a new task
    setFormData({
      title: '',
      description: '',
      dueDate: startDate,
      dueTime: format(startDate, 'HH:mm'),
      priority: 'medium' as const,
      categories: [],
      reminders: [],
      useAI: true
    });
    
    setSelectedEvent(null);
    setIsNewTaskDialogOpen(true);
  };

  // Handle saving a task
  const handleSaveTask = async (taskData: ExtendedTaskFormValues) => {
    try {
      setIsLoading(true);
      
      // Combine date and time for deadline
      const dueDate = new Date(taskData.dueDate);
      const [hours, minutes] = taskData.dueTime.split(':').map(Number);
      dueDate.setHours(hours, minutes);
      
      // Create task DTO
      const taskDTO = {
        title: taskData.title,
        content: taskData.description || '',
        deadline: dueDate.toISOString(),
        priority: taskData.priority.toUpperCase(),
        categories: taskData.categories,
        reminders: taskData.reminders,
        useAI: taskData.useAI
      };
      
      // Save task to API
      const response = await taskService.createTask(taskDTO);
      
      // Add new task to events
      const newEvent = mapTaskToCalendarEvent(response);
      setEvents(prev => [...prev, newEvent]);
      
      // Show success message
      toast({
        title: 'Success',
        description: 'Task created successfully.',
      });
      
      // Reset form data
      setFormData({
        title: '',
        description: '',
        dueDate: new Date(),
        dueTime: format(new Date(), 'HH:mm'),
        priority: 'medium',
        categories: [],
        reminders: [],
        useAI: true,
      });
      
      return response;
    } catch (error) {
      console.error('Error saving task:', error);
      const errorMessage = error instanceof Error ? error.message : 'Failed to save task. Please try again.';
      toast({
        title: 'Error',
        description: errorMessage,
        variant: 'destructive',
      });
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  if (isLoading && events.length === 0) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  return (
    <div className="flex flex-col h-full">
      {/* Mobile header with menu button */}
      <div className="md:hidden flex items-center justify-between p-2 border-b">
        <Sheet open={sidebarOpen} onOpenChange={setSidebarOpen}>
          <SheetTrigger asChild>
            <Button variant="ghost" size="icon">
              <PanelLeft className="h-5 w-5" />
              <span className="sr-only">Toggle sidebar</span>
            </Button>
          </SheetTrigger>
          <SheetContent side="left" className="p-0 w-[280px] sm:w-[300px]">
            <CalendarSidebar
              selectedDate={selectedDate}
              onDateSelect={(date) => {
                setSelectedDate(date);
                const calendarApi = calendarRef.current?.getApi();
                if (calendarApi) {
                  calendarApi.gotoDate(date);
                }
                if (isMobile) setSidebarOpen(false);
              }}
              tasks={[]} // Pass your tasks here
              categories={categories}
              selectedCategories={selectedCategories}
              onCategoryToggle={handleCategoryToggle}
              onTaskClick={handleTaskClick}
            />
          </SheetContent>
        </Sheet>
        <h1 className="text-xl font-semibold">
          {format(selectedDate, 'MMMM yyyy')}
        </h1>
        <div className="w-10"></div> {/* Spacer for flex layout */}
      </div>
      
      <div className="flex flex-1 overflow-hidden">
        {/* Desktop Sidebar */}
        <div className={`hidden md:block ${sidebarOpen ? 'w-80' : 'w-0'} transition-all duration-300 border-r`}>
          {sidebarOpen && (
            <CalendarSidebar
              selectedDate={selectedDate}
              onDateSelect={(date) => {
                setSelectedDate(date);
                const calendarApi = calendarRef.current?.getApi();
                if (calendarApi) {
                  calendarApi.gotoDate(date);
                }
              }}
              tasks={[]} // Pass your tasks here
              categories={categories}
              selectedCategories={selectedCategories}
              onCategoryToggle={handleCategoryToggle}
              onTaskClick={handleTaskClick}
            />
          )}
        </div>
        
        {/* Main Content */}
        <div className="flex-1 flex flex-col overflow-hidden">
          <div className="flex justify-between items-center mb-4">
            <div className="flex space-x-2">
              <Button
                variant={currentView === 'dayGridMonth' ? 'default' : 'outline'}
                size="sm"
                onClick={() => changeView('dayGridMonth')}
              >
                <Grid className="h-4 w-4 mr-2" />
                Month
              </Button>
              <Button
                variant={currentView === 'timeGridWeek' ? 'default' : 'outline'}
                size="sm"
                onClick={() => changeView('timeGridWeek')}
              >
                <CalendarIcon className="h-4 w-4 mr-2" />
                Week
              </Button>
              <Button
                variant={currentView === 'timeGridDay' ? 'default' : 'outline'}
                size="sm"
                onClick={() => changeView('timeGridDay')}
              >
                <CalendarIcon className="h-4 w-4 mr-2" />
                Day
              </Button>
              <Button
                variant={currentView === 'listWeek' ? 'default' : 'outline'}
                size="sm"
                onClick={() => changeView('listWeek')}
              >
                <List className="h-4 w-4 mr-2" />
                List
              </Button>
            </div>
            <Button
              onClick={() => {
                const now = new Date();
                
                // Initialize form data for a new task
                setFormData({
                  title: '',
                  description: '',
                  dueDate: now,
                  dueTime: format(now, 'HH:mm'),
                  priority: 'medium' as const,
                  categories: [],
                  reminders: [],
                  useAI: true
                });
                
                setSelectedEvent(null);
                setIsNewTaskDialogOpen(true);
                
                // Update calendar view
                const calendarApi = calendarRef.current?.getApi();
                if (calendarApi) {
                  calendarApi.changeView('dayGridMonth');
                  calendarApi.gotoDate(now);
                }
              }}
              variant="default"
              size="sm"
            >
              <Plus className="h-4 w-4 mr-2" />
              Add Task
            </Button>
          </div>

          <Dialog open={isNewTaskDialogOpen} onOpenChange={setIsNewTaskDialogOpen}>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Create New Task</DialogTitle>
                <DialogDescription>
                  Add a new task to your calendar
                </DialogDescription>
              </DialogHeader>
              <NewTaskDialog
                onTaskAdded={() => {
                  setIsNewTaskDialogOpen(false);
                  fetchTasks();
                }}
                onCancel={() => setIsNewTaskDialogOpen(false)}
                initialValues={formData}
                onSave={handleSaveTask}
                categories={categories}
              />
            </DialogContent>
          </Dialog>

          <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>{selectedEvent?.title}</DialogTitle>
                <DialogDescription>
                  {selectedEvent?.extendedProps?.description || 'No description provided.'}
                </DialogDescription>
              </DialogHeader>
              <div className="grid gap-4 py-4">
                <div className="grid grid-cols-4 items-center gap-4">
                  <Clock className="h-4 w-4" />
                  <div className="col-span-3">
                    {selectedEvent?.start ? format(new Date(selectedEvent.start), 'PPP p') : 'No date set'}
                  </div>
                </div>
                {selectedEvent?.extendedProps?.priority && (
                  <div className="grid grid-cols-4 items-center gap-4">
                    <AlertTriangle className="h-4 w-4" />
                    <div className="col-span-3">
                      <span className={cn(
                        'inline-block px-2 py-1 text-xs rounded-full',
                        PRIORITY_COLORS[selectedEvent.extendedProps.priority.charAt(0).toUpperCase() + selectedEvent.extendedProps.priority.slice(1).toLowerCase() as keyof typeof PRIORITY_COLORS]
                      )}>
                        {selectedEvent.extendedProps.priority}
                      </span>
                    </div>
                  </div>
                )}
                {selectedEvent?.extendedProps?.categories?.length > 0 && (
                  <div className="grid grid-cols-4 items-center gap-4">
                    <div className="h-4 w-4 flex items-center justify-center">
                      <span className="block h-3 w-3 rounded-full bg-blue-500"></span>
                    </div>
                    <div className="col-span-3 flex flex-wrap gap-1">
                      {selectedEvent.extendedProps.categories.map((category: any) => (
                        <span key={category.id} className="inline-block px-2 py-1 text-xs rounded-full bg-gray-100">
                          {category.name}
                        </span>
                      ))}
                    </div>
                  </div>
                )}
              </div>
              <div className="flex justify-end gap-2">
                <Button
                  onClick={() => {
                    // Handle edit task
                    setIsDialogOpen(false);
                  }}
                  variant="outline"
                >
                  Edit
                </Button>
                <Button
                  onClick={() => {
                    // Handle mark as complete
                    setIsDialogOpen(false);
                  }}
                >
                  <Check className="h-4 w-4 mr-2" />
                  Mark Complete
                </Button>
              </div>
            </DialogContent>
          </Dialog>

          <div className="flex-1 overflow-auto">
            <FullCalendar
              ref={calendarRef}
              plugins={[dayGridPlugin, timeGridPlugin, interactionPlugin, listPlugin]}
              initialView={currentView}
              headerToolbar={false} // We're using our own header
              events={events}
              eventClick={handleEventClick}
              dateClick={handleDateClick}
              select={handleDateSelect}
              selectable={true}
              selectMirror={true}
              dayMaxEvents={true}
              weekends={true}
              height="100%"
              eventTimeFormat={{
                hour: '2-digit',
                minute: '2-digit',
                hour12: true
              }}
              views={{
                dayGridMonth: {
                  dayMaxEventRows: 3,
                },
                timeGridWeek: {
                  dayMaxEventRows: 4,
                },
                timeGridDay: {
                  dayMaxEventRows: 6,
                },
                listWeek: {
                  type: 'listWeek',
                  duration: { weeks: 1 },
                },
              }}
            />
          </div>
        </div>
      </div>
    </div>
  );
}
