

/// Enum for collaborator roles
enum CollaboratorRole { editor, viewer }

/// Enum for user roles in a grocery list
enum UserRole { owner, editor, viewer }

/// Model class for collaborator user information
class CollaboratorUser {
  final String id;
  final String name;
  final String email;

  CollaboratorUser({
    required this.id,
    required this.name,
    required this.email,
  });

  factory CollaboratorUser.fromJson(Map<String, dynamic> json) {
    return CollaboratorUser(
      id: json['_id'] ?? json['id'] ?? '',
      name: json['name'] ?? '',
      email: json['email'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      '_id': id,
      'name': name,
      'email': email,
    };
  }
}

/// Model class for grocery list collaborators
class GroceryCollaborator {
  final CollaboratorUser userId;
  final CollaboratorRole role;
  final DateTime joinedAt;
  final CollaboratorUser invitedBy;

  GroceryCollaborator({
    required this.userId,
    required this.role,
    required this.joinedAt,
    required this.invitedBy,
  });

  factory GroceryCollaborator.fromJson(Map<String, dynamic> json) {
    return GroceryCollaborator(
      userId: CollaboratorUser.fromJson(json['userId'] as Map<String, dynamic>),
      role: CollaboratorRole.values.firstWhere(
        (e) => e.name == json['role'],
        orElse: () => CollaboratorRole.viewer,
      ),
      joinedAt: DateTime.parse(json['joinedAt'] as String),
      invitedBy: CollaboratorUser.fromJson(json['invitedBy'] as Map<String, dynamic>),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'userId': userId.toJson(),
      'role': role.name,
      'joinedAt': joinedAt.toIso8601String(),
      'invitedBy': invitedBy.toJson(),
    };
  }
}

/// Model class for grocery list share settings
class GroceryShareSettings {
  final bool allowCollaboratorInvites;
  final bool requireApprovalForEdits;
  final bool notifyOnChanges;

  GroceryShareSettings({
    required this.allowCollaboratorInvites,
    required this.requireApprovalForEdits,
    required this.notifyOnChanges,
  });

  factory GroceryShareSettings.fromJson(Map<String, dynamic> json) {
    return GroceryShareSettings(
      allowCollaboratorInvites: json['allowCollaboratorInvites'] as bool? ?? false,
      requireApprovalForEdits: json['requireApprovalForEdits'] as bool? ?? false,
      notifyOnChanges: json['notifyOnChanges'] as bool? ?? true,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'allowCollaboratorInvites': allowCollaboratorInvites,
      'requireApprovalForEdits': requireApprovalForEdits,
      'notifyOnChanges': notifyOnChanges,
    };
  }

  GroceryShareSettings copyWith({
    bool? allowCollaboratorInvites,
    bool? requireApprovalForEdits,
    bool? notifyOnChanges,
  }) {
    return GroceryShareSettings(
      allowCollaboratorInvites: allowCollaboratorInvites ?? this.allowCollaboratorInvites,
      requireApprovalForEdits: requireApprovalForEdits ?? this.requireApprovalForEdits,
      notifyOnChanges: notifyOnChanges ?? this.notifyOnChanges,
    );
  }
}

/// Model class for item counts in a grocery list
class ItemCounts {
  final int total;
  final int checked;
  final int unchecked;

  ItemCounts({
    required this.total,
    required this.checked,
    required this.unchecked,
  });

  factory ItemCounts.fromJson(Map<String, dynamic> json) {
    return ItemCounts(
      total: json['total'] as int? ?? 0,
      checked: json['checked'] as int? ?? 0,
      unchecked: json['unchecked'] as int? ?? 0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'total': total,
      'checked': checked,
      'unchecked': unchecked,
    };
  }
}

/// Model class for grocery lists
class GroceryList {
  final String id;
  final String userId;
  final String? name;
  final bool isShared;
  final List<GroceryCollaborator> collaborators;
  final GroceryShareSettings shareSettings;
  final DateTime lastCollaborativeActivity;
  final int? collaboratorCount;
  final UserRole? userRole;
  final ItemCounts? itemCounts;
  final DateTime createdAt;
  final DateTime updatedAt;

  GroceryList({
    required this.id,
    required this.userId,
    this.name,
    required this.isShared,
    required this.collaborators,
    required this.shareSettings,
    required this.lastCollaborativeActivity,
    this.collaboratorCount,
    this.userRole,
    this.itemCounts,
    required this.createdAt,
    required this.updatedAt,
  });

  factory GroceryList.fromJson(Map<String, dynamic> json) {
    return GroceryList(
      id: json['_id'] ?? json['id'] ?? '',
      userId: json['userId'] ?? '',
      name: json['name'] as String?,
      isShared: json['isShared'] as bool? ?? false,
      collaborators: (json['collaborators'] as List<dynamic>?)
          ?.map((item) => GroceryCollaborator.fromJson(item as Map<String, dynamic>))
          .toList() ?? [],
      shareSettings: json['shareSettings'] != null
          ? GroceryShareSettings.fromJson(json['shareSettings'] as Map<String, dynamic>)
          : GroceryShareSettings(
              allowCollaboratorInvites: false,
              requireApprovalForEdits: false,
              notifyOnChanges: true,
            ),
      lastCollaborativeActivity: json['lastCollaborativeActivity'] != null
          ? DateTime.parse(json['lastCollaborativeActivity'] as String)
          : DateTime.now(),
      collaboratorCount: json['collaboratorCount'] as int?,
      userRole: json['userRole'] != null
          ? UserRole.values.firstWhere(
              (e) => e.name == json['userRole'],
              orElse: () => UserRole.viewer,
            )
          : null,
      itemCounts: json['itemCounts'] != null
          ? ItemCounts.fromJson(json['itemCounts'] as Map<String, dynamic>)
          : null,
      createdAt: json['createdAt'] != null
          ? DateTime.parse(json['createdAt'] as String)
          : DateTime.now(),
      updatedAt: json['updatedAt'] != null
          ? DateTime.parse(json['updatedAt'] as String)
          : DateTime.now(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      '_id': id,
      'userId': userId,
      if (name != null) 'name': name,
      'isShared': isShared,
      'collaborators': collaborators.map((c) => c.toJson()).toList(),
      'shareSettings': shareSettings.toJson(),
      'lastCollaborativeActivity': lastCollaborativeActivity.toIso8601String(),
      if (collaboratorCount != null) 'collaboratorCount': collaboratorCount,
      if (userRole != null) 'userRole': userRole!.name,
      if (itemCounts != null) 'itemCounts': itemCounts!.toJson(),
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
    };
  }

  GroceryList copyWith({
    String? id,
    String? userId,
    String? name,
    bool? isShared,
    List<GroceryCollaborator>? collaborators,
    GroceryShareSettings? shareSettings,
    DateTime? lastCollaborativeActivity,
    int? collaboratorCount,
    UserRole? userRole,
    ItemCounts? itemCounts,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return GroceryList(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      name: name ?? this.name,
      isShared: isShared ?? this.isShared,
      collaborators: collaborators ?? this.collaborators,
      shareSettings: shareSettings ?? this.shareSettings,
      lastCollaborativeActivity: lastCollaborativeActivity ?? this.lastCollaborativeActivity,
      collaboratorCount: collaboratorCount ?? this.collaboratorCount,
      userRole: userRole ?? this.userRole,
      itemCounts: itemCounts ?? this.itemCounts,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
}
