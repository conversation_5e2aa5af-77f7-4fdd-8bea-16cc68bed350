# Active Context: Task OrganAIzer

## Current Focus: Mobile Shared Groceries Implementation - COMPLETED (2024-12-30)

### Implementation Summary:
- **Objective**: ✅ COMPLETED - Implement comprehensive shared groceries functionality in the Flutter mobile app
- **Status**: Full implementation completed with all collaboration features, UI components, and backend integration

### Completed Implementation:
1. **✅ Enhanced Data Models**: Extended grocery models with collaboration tracking fields (addedBy, checkedBy, lastModifiedBy, etc.)
2. **✅ Collaboration UI Components**: Created collaboration panel, shared list selector, and enhanced grocery item widgets
3. **✅ Provider State Management**: Enhanced grocery provider with collaboration state and methods for shared list operations
4. **✅ API Integration**: Updated grocery service with all collaboration endpoints using existing v2 API
5. **✅ Visual Collaboration Indicators**: Added collaboration info display in grocery items when viewing shared lists
6. **✅ Popup-Based Navigation**: Implemented collaboration access via popups following scope document design

### Technical Implementation Completed:
- **✅ Enhanced Models**: CollaboratorInfo, GroceryList, GroceryListInvitation, and enhanced GroceryItem with collaboration fields
- **✅ New UI Components**: SharedGroceriesScreen, SharedListSelector, CollaborationPanel, GroceryQuickAddWidget
- **✅ Enhanced Existing Components**: GroceryListItem with collaboration info, GroceriesScreen with collaboration buttons
- **✅ Provider Enhancement**: Extended GroceryProvider with collaboration state, shared list switching, and invitation management
- **✅ Service Integration**: Updated GroceryService with all collaboration methods using existing backend endpoints
- **✅ Test Coverage**: Comprehensive test suite with 8 passing tests covering all collaboration models and functionality

### Current Status:
- **Backend**: ✅ All collaboration endpoints already implemented and fully support mobile features
- **Mobile App**: ✅ Complete feature parity with web app for shared groceries functionality
- **UI/UX**: ✅ Popup-based collaboration interface following scope document design
- **Testing**: ✅ All tests passing, ready for integration testing and deployment

### Backend Support Verification:
- **✅ Collaboration Tracking**: Backend GroceryItem model includes all collaboration fields (addedBy, checkedBy, lastModifiedBy, checkedAt)
- **✅ API Population**: Backend properly populates collaboration fields when returning grocery items
- **✅ Complete Endpoints**: All required collaboration endpoints exist and are fully functional
- **✅ Permission System**: Role-based access control (owner/editor/viewer) fully implemented
- **✅ Invitation System**: Complete invitation lifecycle with token generation and email notifications
- **✅ Real-time Updates**: Socket integration for collaborative actions and activity tracking

---

## Previous Focus: Default Grocery List Preferences System - COMPLETED (2025-05-27)

### Implementation Summary:
- **Objective**: ✅ COMPLETED - Implement default list preferences system for grocery quick-add and AI suggestions
- **Status**: Full implementation completed with comprehensive backend API, frontend UI, and context-aware quick-add functionality

### Completed Implementation:
1. **✅ Backend API (V2 Settings)**: Complete grocery preferences API with get/update endpoints
2. **✅ Frontend Components**: Full preferences UI with settings page and quick access from grocery page
3. **✅ Context-Aware Quick Add**: Enhanced quick-add to respect user's default list preferences
4. **✅ API Endpoint Fixes**: Corrected all settings API endpoints after route restructuring
5. **✅ User Preferences Integration**: Complete integration with existing user preferences system

### Technical Implementation Completed:
- **✅ V2 Settings API**: `/api/settings/grocery-preferences` endpoints for get/update operations
- **✅ Frontend Service**: `grocery-preferences-service.ts` and `user-preferences-service.ts` integration
- **✅ Settings UI**: Comprehensive grocery preferences settings page with shared list selection
- **✅ Quick Add Enhancement**: Context-aware quick add that respects default list preferences
- **✅ API Path Fixes**: Corrected all settings endpoints from `/users/settings/*` to `/settings/*`

### Current Status:
- **Backend**: ✅ All grocery preferences endpoints working and tested
- **Frontend**: ✅ All UI components implemented and integrated with user preferences
- **Quick Add**: ✅ Both text and voice quick-add respect default list preferences
- **Settings Access**: ✅ Available from both main settings menu and quick access from grocery page

---

## Previous Focus: Grocery List Collaboration Features - COMPLETED (2025-05-27)

### Implementation Summary:
- **Objective**: ✅ COMPLETED - Implement shareable grocery lists for families with real-time collaboration
- **Status**: Full implementation completed with comprehensive frontend and backend features

### Completed Implementation:
1. **✅ Backend API (V2 Routes)**: Complete collaboration API with all endpoints
2. **✅ Frontend Components**: Full UI implementation with collaboration panels and shared list browsing
3. **✅ Database Integration**: Extended existing GroceryList model with collaboration features
4. **✅ Role-Based Permissions**: Owner/Editor/Viewer roles with proper access control
5. **✅ User Management**: Email-based collaborator invitations and management
6. **✅ Real-time State**: Context-based state management for collaborative features

### Technical Implementation Completed:
- **✅ V2 API Endpoints**: All collaboration routes properly registered and validated
- **✅ Role-Based Access**: Owner (full control), Editor (add/edit items), Viewer (view only)
- **✅ Email Integration**: Notification system for collaborator invitations
- **✅ Error Handling**: Comprehensive error messages and validation
- **✅ UI Components**: GroceryCollaborationPanel, SharedListsBrowser, GroceryListHeader
- **✅ State Management**: Real-time collaboration context with proper error handling

### Current Status:
- **Backend**: ✅ All collaboration endpoints working and tested
- **Frontend**: ✅ All UI components implemented and integrated
- **Testing**: ✅ Basic functionality verified, ready for multi-user testing
- **Documentation**: 🔄 Currently updating documentation and memory bank

---

## Previous Progress: Mobile AI Insights Navigation Implementation (2025-05-24)

### Changes Made:
- **Enhanced AI Insights Navigation in Mobile App:**
  - **AI Insights List Widget Updates:**
    - Added navigation function to `AiInsightsListWidget` to enable task detail navigation from AI insights
    - Implemented proper routing to task detail screens when interacting with AI autocorrection and interpretation insights
    - Added `onEditTask` callback parameter to enable custom navigation handling from parent components
    - Integrated with Go Router for consistent navigation patterns
  - **Insight Card Widget Enhancements:**
    - Updated `InsightCardWidget` to accept a navigation function for task detail access
    - Modified the `_navigateToTaskDetail` method to use either the provided callback or default navigation
    - Ensured proper task ID passing between components for accurate navigation
    - Maintained backward compatibility with existing insight card implementations
  - **Integration with Existing Components:**
    - Ensured all AI insight types (`AI_AUTOCORRECTION_REVIEW` and `CONFIRM_AI_INTERPRETATION`) properly navigate to their related tasks
    - Verified navigation works correctly from both the insights tab and dashboard previews
    - Maintained loading state handling during navigation to prevent multiple taps
    - Preserved existing insight action functionality while adding navigation capabilities
  - **Code Quality Improvements:**
    - Added proper parameter documentation for new navigation functions
    - Ensured type safety with nullable function parameters
    - Maintained consistent navigation patterns across the application
    - Implemented graceful fallbacks when navigation functions aren't provided

### Rationale:
- Enhancing the navigation between AI insights and related tasks improves the user experience
- Users need to easily access and edit tasks referenced in AI autocorrection and interpretation insights
- Consistent navigation patterns between insights and tasks create a more intuitive workflow
- The callback-based approach allows for flexible navigation handling in different contexts
- Direct task access from insights makes AI features more practical and useful in daily workflows

### Impact:
- Users can now seamlessly navigate between AI insights and their related tasks
- Improved workflow for reviewing and acting on AI autocorrections and interpretations
- Enhanced user experience with direct access to tasks from their AI-generated insights
- More intuitive interaction with AI features through connected navigation
- Consistent navigation patterns across the application for better usability

### Next Steps:
- Add comprehensive tests for the AI insights navigation functionality
- Consider adding deep linking support for sharing AI insights
- Implement analytics to track user interaction with AI insights and subsequent task edits
- Optimize the navigation performance for large numbers of insights
- Consider adding a "related insights" section to the task detail screen for bidirectional navigation

---

## Previous Progress: Enhanced Location Search UI Implementation (2025-05-23)

### Changes Made:
- **Redesigned Location Search Experience:**
  - **New MapWithSearch Component:**
    - Created a dedicated widget that combines map view with search functionality
    - Implemented a central marker that stays fixed in the center of the map view
    - Added address search with typeahead functionality using Mapbox Geocoding API
    - Integrated reverse geocoding to show the current address at the map center
    - Added "Add Here" and "Current Location" buttons for intuitive interaction
  - **UI/UX Improvements:**
    - Redesigned to match the web experience while optimizing for mobile touch interactions
    - Implemented natural gesture handling (pan, pinch to zoom) for map navigation
    - Added visual feedback for the currently selected location
    - Improved address display with automatic updates during map movement
    - Enhanced error handling and loading states for geocoding operations
  - **Technical Enhancements:**
    - Implemented debounced reverse geocoding to reduce API calls during map movement
    - Added support for pre-filling address in location dialog from search or reverse geocoding
    - Improved map marker styling and animations
    - Simplified the location addition workflow

### Rationale:
- Previous implementation required users to toggle an "Add Location Mode" before tapping on the map
- New approach with central marker provides clearer visual indication of what location will be added
- Web-like experience with search bar and map integration provides consistency across platforms
- Direct "Add Here" button simplifies the workflow compared to the previous mode-based approach
- Automatic address lookup reduces manual typing and improves accuracy

### Impact:
- More intuitive location search and add experience for mobile users
- Consistent user experience between web and mobile platforms
- Improved geocoding integration with better error handling
- Reduced friction in the location addition workflow
- Enhanced visual feedback during map interaction
- Better code organization with a dedicated MapWithSearch component

### Next Steps:
- Add comprehensive tests for the new location search functionality
- Consider adding location clustering for areas with many markers
- Implement location filtering options similar to the web interface
- Add support for custom marker icons and colors during location creation
- Optimize geocoding API usage to reduce costs

---

## Previous Progress: Completed API Response Format Standardization (2025-05-21)

### Changes Made:
- **Completed Standardized API Response Format Implementation:**
  - **Standardized Structure:**
    - Updated all API responses to follow a consistent format: `{ success: true/false, data: [...], pagination: {...}, error: {...} }`
    - Added `success` field to all responses to indicate operation status
    - Structured error responses with `{ success: false, error: { message: '...', code: '...' } }`
    - Standardized pagination format with `{ currentPage, totalPages, pageSize, totalItems }`
  - **Controller Updates:**
    - Modified non-V2 controllers to use the standardized format (taskController.ts, categoryController.ts, groceryController.ts, etc.)
    - Fixed inconsistencies in V2 controllers (userControllerV2.ts, authControllerV2.ts)
    - Added error codes for better error handling and client-side parsing
    - Ensured all responses include the `success` field
    - **Completed V2 Controller Updates:**
      - Updated userSettingsController.ts with standardized error responses
      - Fixed userController.ts to use the standardized format
      - Verified that all V2 controllers (authController.ts, categoryController.ts, locationController.ts, taskController.ts) follow the standardized format
  - **Parser Updates:**
    - Updated frontend and mobile app parsers to handle the standardized format
    - Enhanced BaseService class to support both legacy and new response formats
    - Ensured backward compatibility during transition

### Rationale:
- Inconsistent API response formats were causing issues for both web and mobile clients
- Standardized format simplifies client-side parsing and error handling
- Consistent error responses improve debugging and user feedback
- Standardized pagination format makes list handling more predictable
- Common format between V2 and non-V2 endpoints reduces code duplication
- Error codes provide a more structured way to handle specific error conditions

### Impact:
- Improved API consistency across all endpoints
- Simplified client-side parsing with predictable response structure
- Better error handling with specific error codes
- More robust pagination handling
- Reduced code duplication in client-side parsers
- Improved developer experience with consistent API behavior
- Enhanced error handling capabilities with standardized error codes
- Better maintainability with consistent response patterns

### Next Steps:
- Add comprehensive tests for the standardized response format
- Update API documentation to reflect the new format
- Monitor for any issues during the transition period
- Consider adding more specific error codes for better error handling
- Implement global error handling in frontend and mobile app based on standardized error codes

---

## Previous Progress: Fixed QuickAdd Task Item Priority Border (2025-05-16)

### Changes Made:
- **Fixed Priority Border for QuickAdd Tasks in Mobile App:**
  - **Root Cause Analysis:**
    - Tasks created via QuickAdd were not showing their priority border color on initial creation
    - When editing the task (even with no changes), the border would appear correctly
    - Debug logs showed that QuickAdd was sending `priority: null` in API responses
    - The AI prompt in `aiPrompts.yaml` instructed the AI to only set priority when specific keywords are present, otherwise return null
    - The task_item.dart widget would show no border color when priority was null
  - **Solution Implemented:**
    - Modified `aiController.ts` to ensure all created tasks have a priority value: `priority: priority || 'Medium'`
    - This approach ensures that even when the AI returns null for the priority, the task still gets a default 'Medium' priority
    - The default value is applied during task creation in the backend
  - **Additional Findings:**
    - The AI prompt is designed to only set priority when specific keywords like "urgent", "ASAP", or "important" are detected
    - If no priority-related keywords are found, the prompt explicitly instructs the AI to return null
    - Our backend code change is a better solution than changing the prompt as it maintains AI behavior while ensuring UI consistency

### Rationale:
- Users expect visual cues like priority borders for all tasks
- Consistent UI behavior is important for user experience
- Applying a default priority ensures all tasks have a visual priority indicator
- Fixing at the backend level is more robust than changing the frontend rendering logic

### Impact:
- Tasks created via QuickAdd now correctly display their priority border on first render
- Improved UI consistency between task creation methods (QuickAdd vs. standard form)
- Users can now identify task priorities immediately upon creation
- The fix is subtle and maintains expected behavior while improving visual consistency

### Next Steps:
- Monitor for any unexpected behavior with the priority assignment
- Consider updating the AI prompt in the future to suggest default priorities even when keywords aren't present
- Add more comprehensive testing for the QuickAdd functionality
- Review other potential UI inconsistencies between QuickAdd and standard task creation

---

## Latest Progress: Mobile App Backend Connection Fix (2025-05-16)

### Changes Made:
- **Fixed Mobile App Backend Connection in Production:**
  - **API Configuration Enhancement:**
    - Modified `api_config.dart` to ensure consistent backend URL usage in production builds
    - Added explicit checks to use production URL (`https://flashtasks-ai.onrender.com`) for Firebase-deployed APKs
    - Improved detection of release mode and production environment
    - Added additional logging for debugging connection issues
  - **Environment-Aware URL Selection:**
    - Updated `getBaseUrl()` method to prioritize production URL for release builds
    - Added early URL initialization in `_detectProductionMode()`
    - Prevented URL overrides in production mode via settings screens
  - **Improved Debugging and Testing:**
    - Added verification method for testing in release mode (`flutter run --release`)
    - Enhanced error logging for network connectivity issues
    - Added platform-specific network configuration checks

### Rationale:
- Users running the APK from Firebase were having issues connecting to the backend and couldn't log in
- The app was not consistently using the production Render backend in release builds
- Better environment detection was needed to handle the transition from development to production
- Improved logging will help identify future issues faster

### Impact:
- Users can now successfully log in when using the Firebase-deployed APK
- The app consistently uses the correct backend URL based on build type
- Development experience is preserved with local backend URLs for testing
- Release builds automatically use the production Render backend
- Production URL can't be accidentally changed via settings

### Next Steps:
- Consider implementing automatic backend selection based on network connectivity
- Add a network connectivity check on app startup
- Implement a more robust error handling system for network failures
- Consider adding an offline mode for better user experience when backend is unavailable
- Add more comprehensive analytics for tracking connection issues in production

---

## Latest Progress: Mobile App Permission Implementation (2025-05-15)

### Changes Made:
- **Implemented Mobile App Permission Handling:**
  - **Platform-specific Permission Declarations:**
    - Added location and microphone permissions to `AndroidManifest.xml`
    - Added usage descriptions to iOS `Info.plist` for location and microphone
  - **Permission Service Layer:**
    - Created a centralized `PermissionsService` class for managing all app permissions
    - Implemented methods for requesting individual permissions and all permissions at once
    - Added utility functions to check current permission statuses
    - Implemented app settings navigation for denied permissions
  - **User Interface Components:**
    - Created a reusable `PermissionRequestDialog` for user-friendly permission requests
    - Implemented explanations for why each permission is needed
    - Added visual feedback for permission states
    - Included settings navigation for permanently denied permissions
  - **App Integration:**
    - Updated `main.dart` to request permissions during app initialization
    - Modified `app.dart` to check permission status after app is built
    - Implemented background permission checking to track permission changes

### Rationale:
- Location and microphone permissions are essential for key app features (location-based tasks and voice input)
- Permission handling needed to follow best practices with clear user explanations
- Centralized permission management improves code organization and maintainability
- User-friendly dialogs improve permission acceptance rates
- Early permission requests ensure features work immediately when needed

### Impact:
- App now properly requests required permissions on launch
- Users receive clear explanations about why each permission is needed
- Permission states are properly tracked and managed
- Features requiring permissions can gracefully handle denied states
- App complies with platform guidelines for permission handling

### Next Steps:
- Implement feature-specific permission checks before using location or microphone
- Add permission requirement for notifications
- Consider adding offline mode for users who deny location permission
- Improve voice input features now that microphone permission is handled

---

## Previous Progress: Mobile App Server Configuration (2025-05-12)

### Changes Made:
- **Implemented Mobile App Server Configuration:**
  - **Server Settings Screen:**
    - Created a dedicated `ServerSettingsScreen` for configuring the backend server URL
    - Added UI for entering custom IP address and port
    - Added test connection functionality to verify connection before login
    - Built persistence mechanism using SharedPreferences
  - **Access Without Authentication:**
    - Modified the router to allow access to server settings without requiring login
    - Added a "Having connection issues?" button on the login screen
    - Created a direct route to server settings that bypasses authentication
  - **API Configuration Enhancement:**
    - Updated `ApiConfig` class to load saved server URL on app startup
    - Implemented URL change notification mechanism to refresh all API clients
    - Modified `dioProvider` to watch for URL changes using Riverpod
    - Created `apiBaseUrlProvider` to provide the current URL to all API clients
  - **Comprehensive Error Handling:**
    - Added visual feedback for connection test results
    - Enhanced error messages for connection failures
    - Added logging for tracking URL changes and connection attempts

### Rationale:
- Physical devices needed a way to connect to local development servers (instead of using 10.0.2.2 which only works in emulators)
- Users were encountering connection timeouts when trying to connect from physical devices
- The app needed a configuration mechanism accessible without login (to solve the catch-22 of needing to login to configure the server)
- The URL configuration needed to be propagated to all API clients to ensure consistent connectivity

### Impact:
- Mobile app can now connect to backend servers from physical devices
- Developers and testers can easily switch between different backend environments
- Users can troubleshoot connection issues without requiring code changes
- Connection configuration persists between app restarts
- All API clients automatically update when server URL changes

### Next Steps:
- Consider adding server environment presets (Dev, Staging, Production)
- Add more comprehensive server connectivity tests
- Consider adding SSL certificate acceptance for HTTPS connections
- Implement a server discovery mechanism for local networks

---

## Latest Progress: Mobile AI Insights Implementation (2025-05-11)

### Changes Made:
- **Implemented AI Insights on Mobile:**
  - **New Action Buttons in Insights Card:**
    - Added action buttons for overdue tasks (Mark Complete, Change Deadline, Delete Task)
    - Added action buttons for duplicate tasks (Review Duplicates)
    - Added action buttons for merge suggestions (Merge Tasks)
  - **New Screens for Insight Actions:**
    - Created `ReviewDuplicatesScreen` for reviewing potential duplicate tasks
    - Created `MergeTasksScreen` for combining similar tasks into a single task
  - **API Integration:**
    - Fixed API integration issues after API consolidation
    - Updated endpoint formats and HTTP methods to match current backend implementation
    - Fixed issues with insight action endpoints (using POST to `/api/ai/insights/:id/action`)
    - Fixed task update endpoints (using PUT instead of PATCH)
  - **Data Handling:**
    - Used `Map<String, dynamic>` approach instead of relying on Task model
    - Implemented direct API calls using Dio with proper configuration

### Rationale:
- The mobile app needed the same AI insights functionality as the web application
- API consolidation introduced breaking changes in how endpoints work
- Task model dependencies were causing compilation errors

### Impact:
- Mobile app now has feature parity with web app for AI insights
- Users can manage overdue tasks, review duplicates, and merge similar tasks
- Improved error handling and user feedback
- More consistent and reliable API communication

### Next Steps:
- Add more advanced AI insights features as they're developed on the web app
- Consider implementing a shared API client library for consistent endpoint usage
- Add more comprehensive error handling for edge cases

---

## Latest Progress: Android Network Security Configuration Fix (2025-05-16)

### Changes Made:
- **Fixed Android App Network Security Issues:**
  - **Network Security Configuration:**
    - Added `network_security_config.xml` to allow cleartext traffic to specific domains
    - Updated Android manifest to reference the security config
    - Added `android:usesCleartextTraffic="true"` to allow non-HTTPS connections
    - Explicitly added Internet permission
  - **Enhanced Error Logging:**
    - Improved API client error logging for better Android debugging
    - Added platform-specific information to Dio error interceptors
    - Added startup logging to check environment and connection info
  - **Network Provider Updates:**
    - Enhanced Dio configuration with Android-specific settings
    - Added connectivity status provider for network state tracking
    - Improved error handling for TLS/SSL handshake issues

### Next Steps:
- Test the Android APK with the new security configuration
- Monitor logs for any remaining connection issues
- Consider implementing certificate pinning for enhanced security in the future

---

## Previous Progress: Enhanced AI Quick Add & CSRF Fix (2025-05-11)

### Changes Made:
- **Evolved `processQuickAdd` AI Functionality (Backend):**
    - **Prompt Enhancement (`backend/src/prompts/aiPrompts.yaml`):**
        - The `processQuickAdd` prompt was significantly updated to improve multi-task segmentation.
        - It now instructs the AI to extract detailed attributes (`title`, `content`, `categoryNames`, `locationName`, `deadline`, `time`, `priority`, `reminders`) for *each individual task* identified within a single user input.
        - Added instructions for the AI to suggest an implicit `suggestedProjectTitle` if multiple parsed tasks seem to belong to a common larger goal.
        - The expected JSON output structure from the AI is now a top-level object containing `suggestedProjectTitle` (string or null) and a `tasks` array (which can hold multiple detailed task objects or a single grocery object).
    - **Controller Logic Update (`backend/src/controllers/aiController.ts`):**
        - The `processQuickAdd` function was modified to parse the new AI output structure.
        - It now iterates through each task object provided by the AI within the `tasks` array.
        - For each task, it uses the *specific attributes* parsed by the AI for that individual task when creating the `Task` document. This includes utilizing services like `locationSuggestionService` and date/reminder processing functions for each task individually.
        - The `suggestedProjectTitle` received from the AI is included in the API response sent back to the client.
        - Existing grocery parsing logic is maintained.
- **CSRF Protection Fix (Backend - `backend/src/routes/v2/auth.ts`):**
    - Corrected the implementation of conditional CSRF protection.
    - The `conditionalCsrfProtection` middleware was updated to use `doubleCsrfProtection` (the standard middleware provided by the `csrf-csrf` library) instead of attempting to call `validateRequest` directly with `req, res, next`. This resolved a TypeScript build error (TS2554).

### Rationale:
- **AI Quick Add Enhancement:** To significantly improve the user experience by allowing more natural, complex, and multi-faceted task input, reducing manual entry and making the AI more intuitive and powerful. This addresses the user's request to improve AI support, especially for quick add.
- **CSRF Fix:** To resolve a backend build error and ensure CSRF protection is correctly and robustly applied as per library best practices.

### Impact:
- **AI Quick Add:**
    - The backend is now capable of understanding more complex user inputs for task creation.
    - Tasks can be created with richer, individually assigned details (location, deadline, categories, etc.) directly from a single quick add entry.
    - The system can now suggest potential project groupings for related tasks.
    - This lays the groundwork for a more intelligent and efficient task input experience on the frontend.
- **CSRF Fix:**
    - Backend build errors related to `auth.ts` should be resolved.
    - CSRF protection for the logout endpoint (and potentially others using this middleware) is now correctly implemented.

### Next Steps (Post-Memory Update):
- **Verify Backend Build:** Confirm the backend builds successfully after the CSRF fix.
- **Thorough Backend Testing:** Test the enhanced `processQuickAdd` endpoint with diverse inputs. Test the `/api/v2/auth/logout` endpoint for CSRF functionality.
- **Frontend Adaptations:** Update frontend clients to utilize the new `suggestedProjectTitle` and handle the potentially richer task data from quick add.
- **Iterative Prompt Refinement:** Continuously test and refine the `processQuickAdd` prompt based on results.

---

## Previous Progress: Location Page Coordinates Type Fix (2025-10-02)

### Issues:
- Frontend build was failing with a TypeScript error related to coordinates type mismatch in the locations page
- The error occurred when trying to assign `LocationModel[]` from the API to the page's `Location[]` state

### Root Cause:
- The LocationModel type in the API allowed coordinates to be either `GeoPoint` or `DirectCoordinates`
- The local Location type in the page component expected only `{ type: 'Point'; coordinates: [number, number] }`
- This type mismatch caused TypeScript errors when assigning API responses to state

### Fix:
- Added type guard functions to properly check coordinate types:
  ```typescript
  const isDirectCoordinates = (coords: any): coords is { latitude: number; longitude: number } => {
    return coords && 'latitude' in coords && 'longitude' in coords;
  };

  const isGeoPoint = (coords: any): coords is { type: 'Point'; coordinates: [number, number] } => {
    return coords && 'type' in coords && coords.type === 'Point' && 'coordinates' in coords;
  };
  ```
- Implemented proper coordinate conversion when setting locations in state:
  ```typescript
  setLocations(locationsData.map(loc => {
    const coordinates = 'type' in loc.coordinates && loc.coordinates.type === 'Point'
      ? loc.coordinates
      : {
          type: 'Point' as const,
          coordinates: [
            'longitude' in loc.coordinates ? loc.coordinates.longitude : 0,
            'latitude' in loc.coordinates ? loc.coordinates.latitude : 0
          ] as [number, number]
        };

    return {
      ...loc,
      coordinates
    } as Location;
  }));
  ```
- Applied the same conversion pattern when adding new locations and editing existing ones

### Impact:
- Frontend build now completes successfully without TypeScript errors
- Proper handling of different coordinate formats from the API
- More robust type safety throughout the locations page component
- Improved error resilience when dealing with location data

### Lessons Learned:
- Type guard functions are valuable for handling different data structures from APIs
- Explicit coordinate format conversion is necessary when dealing with geospatial data
- Consistent coordinate handling throughout the application is important

---

## Latest Progress: Enhanced Authentication & Security Implementation (2025-05-11)

### Changes Made:
- **JWT Token Enhancement**:
  - Updated all authentication endpoints (login, register, refresh) to include complete user information in JWT tokens
  - Tokens now contain userId, email, name, and roles data
  - Keeps refresh tokens minimal for security while providing complete context in access tokens

- **CSRF Protection Refinement**:
  - Enhanced CSRF protection across all state-changing operations (POST, PUT, PATCH, DELETE)
  - Implemented robust error handling for CSRF token validation
  - Added extensive logging to help troubleshoot security issues

- **Admin Route Integration**:
  - Fixed the admin routes by properly mounting them in the v2 API structure
  - Added appropriate path configuration for admin endpoints (`/admin/users`)
  - Ensured admin routes maintain proper role-based access control

### Rationale:
- Users were previously shown as "Unknown" due to missing fields in JWT tokens
- Security testing revealed inconsistencies in CSRF protection and admin route access
- Admin functionality needed to be properly integrated into the v2 API structure

### Impact:
- Improved user experience with correct user information displayed immediately after login
- Enhanced security posture with comprehensive CSRF protection
- Complete role-based access control for admin functionality
- Reduced API calls by including necessary user data in tokens

### Next Steps:
- Consider implementing token blacklisting for immediate invalidation on logout
- Add comprehensive security testing coverage
- Document the security architecture for future development

---

## Previous Progress: Firebase Admin SDK Configuration and Notification Standardization (2025-05-10)

### Changes Made:
- Enhanced `backend/src/config/firebaseAdmin.ts` with robust initialization and error handling
- Implemented dual configuration method support:
  - JSON string in `FIREBASE_SERVICE_ACCOUNT_JSON` environment variable (for production/cloud)
  - File path in `GOOGLE_APPLICATION_CREDENTIALS` environment variable (for local development)
- Added detailed logging for troubleshooting Firebase configuration issues
- Updated `insightService.ts` to ensure consistent notification triggering for all insight types
- Standardized notification payloads across different notification types
- Created comprehensive documentation:
  - Updated `docs/firebase-setup-guide.md` with detailed instructions
  - Added new `backend/FIREBASE-SETUP.md` with implementation-specific details

### Rationale:
- Push notifications weren't working reliably in the alpha deployment due to credential configuration issues
- Firebase Admin SDK initialization lacked proper error handling and troubleshooting information
- Notifications were inconsistently implemented across different insight types
- Developers lacked clear guidance on configuring Firebase credentials

### Impact:
- Enhanced reliability of push notifications across all environments
- Improved developer experience with clear error messages and setup instructions
- Standardized notification triggering ensures consistent user experience
- Better support for mobile app notification handling

### Next Steps:
- Implement task status change notifications (when tasks are completed/blocked)
- Add user preference settings for notification types
- Create testing infrastructure for notification delivery

---

## Previous Progress: V1 Settings Proxy Routes Removal (2025-05-10)

### Changes Made:
- Removed legacy V1 proxy controllers and routes for user settings and category-location mappings
- Deleted `backend/src/controllers/v1/categoryLocationMappingProxyController.ts`
- Deleted `backend/src/routes/userSettings.ts` and `backend/src/routes/categoryLocationMapping.ts`
- Removed associated validation files: `userSettingsValidation.ts` and `categoryLocationMappingValidation.ts`
- Deleted corresponding test file: `category-location-mapping-validation.test.ts`
- Removed imports from `app.ts`

### Rationale:
- The V2 API endpoints (`/api/users/settings/...`) had fully replaced the legacy proxy routes
- The V1 proxy controllers were redundant after API consolidation
- Removing unused code reduces maintenance overhead and improves codebase clarity
- V2 settings API offers more robust functionality and better integration with both web and mobile clients

### Impact:
- No functional impact as application was already using V2 API endpoints
- Backend codebase is now cleaner and more maintainable
- Future developers won't be confused by the presence of unused proxy routes
- All settings functionality continues to work correctly through the V2 API

### API Documentation Update:
- Removed references to deprecated V1 settings endpoints from documentation
- Confirmed V2 settings endpoints (`/api/users/settings/...`) are accurately documented
- Updated internal notes to reflect this technical decision

---

## Previous Progress: V2 Authentication Redirection Fix (2025-05-09)

### Issue:
- After the V2 API migration, login was successful (tokens were correctly stored in localStorage) but users weren't being redirected to the dashboard
- Multiple redirection approaches were attempted without success
- Session persistence was broken, requiring re-login after page refreshes

### Root Cause:
- Tokens were only stored in localStorage, making them inaccessible to Next.js middleware
- Race conditions between state updates and navigation were disrupting the redirection flow
- Multiple conflicting redirection processes in different parts of the code
- Duplicate auth checking useEffect hooks in the dashboard layout

### Fix:
- Implemented dual storage approach (localStorage + cookies) for tokens to support middleware access
- Added explicit token setting call right before redirection
- Used setTimeout to create a delayed redirect that prevents state update conflicts
- Streamlined authentication checking in dashboard layout by removing duplicate useEffect
- Enhanced user data extraction to better handle multiple API response formats

### Lesson Learned:
- Next.js applications with both client and server components require tokens to be available in both localStorage and cookies
- State updates and navigation should be carefully sequenced to prevent race conditions
- Auth flows should consolidate authentication checks in a single location to prevent conflicts
- Always include fallbacks for data extraction to handle varied API response formats

---

## Previous Progress: Map Component Centering Bug & Fix (2025-05-09)

### Issue:
- The map in the frontend locations page wasn't centering on current locations and didn't fly to locations when clicked from the location list.

### Root Cause:
- Location objects used `id` instead of `_id` which caused reference issues.
- The map reference `mapRef` wasn't properly handling the flyTo method.
- Coordinates structure in the location objects had a different format than expected: using `{latitude, longitude}` instead of GeoJSON format `coordinates: [lng, lat]`.

### Fix:
- Created helper functions to safely extract location IDs and coordinates
- Updated the `handleCenterMapOnLocation` function to properly handle different coordinate formats
- Added better error handling and debugging to diagnose coordinate structure issues
- Improved marker rendering to use consistent coordinate extraction
- Modified the `getLocationCoordinates` function to specifically handle the application's format: `{ coordinates: { latitude, longitude }}`

### Lesson Learned:
- When working with map components, ensure consistent coordinate format handling across the application
- Add helper functions for coordinate and ID extraction to handle different formats consistently
- Include proper error handling and debugging for geospatial operations
- Always verify the structure of location data before passing to map components

---

## Latest Progress: Web Category Filtering Bug & Fix (2025-05-08)

### Issue:
- After API consolidation, category filtering in the web app sidebar was broken. Clicking a category did not filter tasks, and the selection state was lost.

### Root Cause:
- The backend API now returns categories with an `id` field (not `_id`).
- The frontend category service was mapping only `_id`, resulting in `undefined` IDs in the UI and filter logic.
- This caused the sidebar to pass `undefined` as the filter, so no filtering or selection worked.

### Fix:
- Updated the frontend category service (`mapBackendCategoryToFrontend`) to map `id` from the backend to `_id` in the frontend model.
- Now, all category and subcategory objects have a valid `_id` in the frontend, restoring correct selection and filtering.

### Lesson Learned:
- After API changes or consolidation, always verify backend/response field names and update frontend mapping logic accordingly.
- Use explicit mapping in service/model layers to avoid silent data mismatches.
- Add targeted logging in the UI to quickly trace data flow and catch mapping issues.

---

## Current Focus
The team has **successfully implemented several key mobile app features** including the location tab with map integration and the settings menu with comprehensive options. These implementations follow the successful API consolidation to maintain feature parity between the frontend web-app and the mobile-app using the v2 API. The focus now shifts to:

1. **API Consolidation:** **(Completed)** Consolidated the two APIs into one using the v2 API to maintain feature parity between the frontend web-app and the mobile-app.
2. **Category Data Granularity Fix (May 2025):**
   - **Issue:** After consolidation, the `/api/categories` endpoint did not expose `isPredefined`, `isAiGenerated`, or `allowUserManagement`, which are required by both web and mobile frontends for correct category management and UI logic.
   - **Backend Change:** Updated the `MobileHierarchicalCategory` interface and the `getMobileCategories` controller to select and return these fields for all categories.
   - **Risk:** Low, but possible issues if legacy category documents are missing these fields or if frontends assume their presence without null checks.
   - **Main Testing Focus:**
     - API returns the new fields for all categories
     - Web: Sidebar/category management logic (edit/delete/labels) works as expected
     - Mobile: Category list, management, and any logic using these flags works with no crashes
     - Regression: All category-related features (listing, hierarchy, task counts) remain functional
3. **Mobile App Feature Completion:** **(Partially Completed)** Successfully implemented several key features in the Flutter mobile app:
   - **(Completed)** Location tab with map integration and the ability to create locations by clicking directly on the map
   - **(Completed)** Settings menu with comprehensive options for user preferences, task settings, notifications, date/time formats, and AI-driven configurations
   - **(Completed)** Task filtering functionality matching the web version
   - **(Completed)** Grocery management feature
   - **(Completed)** AI Insights integration
   - Continue implementing remaining features and refinements
4. **Testing and Quality Assurance:** Maintain and expand test coverage for all API endpoints and mobile features.
5. **Performance Optimization:** Monitor API performance metrics and implement optimizations.
6. **Error Handling:** **(Completed)** Implemented consistent error handling across the entire application stack (backend, frontend, mobile).
7. **Documentation:** **(Updated)** Updated documentation to reflect the current state of the project, particularly the mobile app features.

## Recent Decisions

### Mobile App Category Filtering Fixes
1. **Issues Identified:**
   - After API consolidation, category filtering in the mobile app was not working properly
   - Clicking on categories in the drawer menu did not properly filter tasks
   - The "Uncategorized" filter was not showing uncategorized tasks
   - The "All Tasks" option was not clearing category filters correctly

2. **Root Causes:**
   - **Parameter Name Mismatch:** The mobile app was sending `categoryContextId` as the parameter name, but the backend V2 API expected two different parameters:
     - `categoryContext` for special filters like 'uncategorized_tasks_view'
     - `categories` for regular category IDs
   - **Value Mismatch:** The mobile app was using 'uncategorized' as the value, but the backend expected 'uncategorized_tasks_view'
   - **Filter Clearing Issues:** The copyWith method in TaskFilters class couldn't explicitly set fields to null

3. **Solution Implemented:**
   - **Fixed Parameter Names:** Updated the `toQueryParameters()` method in `TaskFilters` class to:
     - Use 'categoryContext' for special values like 'uncategorized_tasks_view'
     - Use 'categories' for regular category IDs
   - **Fixed Value for Uncategorized:** Changed the value from 'uncategorized' to 'uncategorized_tasks_view' in the drawer menu
   - **Improved Filter Clearing:**
     - Added a dedicated `clearCategoryFilter()` method to the `TaskFilters` class
     - Updated the `TaskNotifier` class with a `clearCategoryFilter()` method
     - Modified the "All Tasks" option to use the new clear method
   - **Updated UI Selection States:** Corrected the selection checks in the drawer to match the new values

4. **Results:**
   - Category filtering now works correctly for all categories
   - Uncategorized items are properly displayed when selected
   - The "All Tasks" option correctly clears all category filters
   - Selection states in the UI correctly reflect the current filters

5. **Lessons Learned:**
   - API consolidation requires careful attention to parameter names and expected values
   - Special handling is needed for explicitly setting properties to null in Dart/Flutter
   - Creating dedicated clear methods is more robust than relying on null parameters

### Mobile App Login Fix
1. **Issue Identified:**
   - After API consolidation, the mobile app was unable to login successfully
   - The login response from the API was successful (status code 200), but the AuthNotifier was reporting a login failure
   - The issue was due to changes in the response format after API consolidation

2. **Solution Implemented:**
   - Updated the `login` method in `AuthService` to handle the new consolidated API response format
   - Added support for both the new format (direct user data with tokens) and the old format (separate user and tokens objects)
   - Updated the `refreshTokens` method to handle both new and old token response formats
   - Updated the `getCurrentUser` method to handle both `userId` and `id` field formats
   - Added detailed logging to help diagnose authentication issues

3. **Results:**
   - Mobile app can now successfully authenticate with the consolidated API
   - Login, token refresh, and user profile retrieval work correctly
   - Backward compatibility with old response formats is maintained

### API Consolidation Implementation
1. **Consolidated Two APIs into One:**
   - Modified backend controllers in `backend/src/controllers/v2/` to return full data models instead of limited fields
   - Renamed methods like `getMobileTasks` to `getTasks`, `getMobileTaskById` to `getTaskById`, etc.
   - Removed `.select()` statements that limited fields and ensured proper population of related documents
   - Changed `app.use('/api/v2', v2Routes)` to `app.use('/api', v2Routes)` in app.ts
   - Removed all old V1 route mounts while keeping shared essential routes like auth, csrf-token, and ping

2. **Frontend Adaptation:**
   - Updated endpoint definitions in `frontend/config.ts` to point to the correct paths
   - Modified BaseService class to handle both legacy and new response formats
   - Updated task-service.ts, category-service.ts, location-service.ts, and user-service.ts to use the new endpoints
   - Added support for the new response format with `success` and `data` fields
   - Maintained backward compatibility with legacy response formats

3. **Mobile App Adaptation:**
   - Removed the `/v2` path segment from all endpoint definitions in `mobile/flashtasks_mobile/lib/src/core/config/api_config.dart`
   - Updated endpoint paths to match the new API structure
   - Changed `taskCompletionEndpoint` to use `toggle` instead of `complete`

4. **Testing and Verification:**
   - Ran backend tests to verify API functionality
   - Built the mobile app to ensure it works with the updated API configuration
   - Identified test failures related to CSRF protection and admin routes for future fixes

### Mobile App Settings Menu Implementation
1. **Implemented Comprehensive Settings Feature:**
   - Created models for all settings types: UserPreferences, TaskSettings, NotificationSettings, DateTimeSettings, KeytagMappingSettings, CategoryLocationMappingSettings, LocationSuggestionSettings, and UserAllSettings
   - Implemented SettingsService to handle API calls for fetching and updating settings
   - Created UserSettingsNotifier and userSettingsProvider to manage settings state
   - Implemented the main SettingsScreen with navigation to sub-screens
   - Created sub-screens for each settings group: ProfileSettingsScreen, AccountSettingsScreen, TaskSettingsScreen, NotificationSettingsScreen, DateTimeSettingsScreen, KeytagMappingSettingsScreen, CategoryLocationMappingSettingsScreen, and LocationSuggestionSettingsScreen
   - Updated the app router to include routes for the settings screens
   - Fixed issues with the category and location providers to ensure they work correctly with the settings screens

2. **Backend V2 API Implementation:**
   - Created a new user settings controller in V2 API to handle settings management
   - Implemented endpoints for getting and updating all settings in a single request
   - Created endpoints for managing specific settings categories (task settings, notification settings, date/time settings, keytag mappings, category-location mappings, location suggestion settings)
   - Implemented proper validation for all settings endpoints
   - Enhanced error handling for settings operations
   - Ensured backward compatibility through V1 to V2 API proxying
   - Added comprehensive logging for settings operations

3. **Mobile App Integration:**
   - Updated the SettingsService to use the V2 API endpoints
   - Implemented robust error handling with fallback to default settings
   - Enhanced settings providers to maintain state consistency even when API calls fail
   - Fixed const constructor issues in settings screens
   - Improved category-location mapping by using locations list provider
   - Fixed issues with displaying existing category-location mappings

### Mobile App Location Tab Implementation
1. **Completed Location Tab Feature:**
   - Implemented comprehensive location management in the Flutter mobile app with MongoDB backend
   - Updated the backend Location model to include the `archived` field
   - Created validation for V2 location endpoints
   - Updated the V2 location routes to include all necessary endpoints
   - Created a Location model with fields for name, address, coordinates, radius, customIcon, customColor, keywords, archived status, and taskCount
   - Developed LocationService to handle API calls for locations (fetching, creating, updating, deleting)
   - Implemented LocationsNotifier using Riverpod for state management
   - Added methods for loading locations, selecting locations, creating, updating, and deleting locations
   - Created UI components including LocationsScreen, LocationListItem, and LocationDetailWidget
   - Integrated flutter_map for interactive map display with location markers
   - Added support for creating new locations with map coordinate selection
   - Implemented a dedicated "add location mode" where users can click directly on the map to create a new location
   - Added visual feedback and UI controls for the add location mode
   - Implemented task association to display tasks linked to specific locations
   - Added geolocation features to get the user's current location
   - Created utility classes for handling location styling (icons and colors)
   - Added loading states and error handling throughout the location feature

2. **Map Integration:**
   - Used flutter_map for an interactive map display
   - Implemented marker creation for all user locations
   - Added color and icon customization for location markers
   - Implemented map camera controls for panning and zooming
   - Added user location tracking with proper permission handling
   - Implemented map tap functionality to create new locations
   - Added a toggle button for "add location mode" with visual feedback
   - Created responsive layouts for both wide (tablet) and narrow (phone) screens

3. **Location Management UI:**
   - Created a detailed location view showing:
     - Location information (name, address, coordinates)
     - Custom styling (icon and color)
     - Associated tasks list with task count
   - Implemented location editing and deletion with confirmation dialogs
   - Added location list view with custom styling based on location properties
   - Created add/edit location dialog with map coordinate selection

4. **Task Integration:**
   - Updated the task service to add the getTasksByLocation method
   - Added the tasksByLocationEndpoint to the API config
   - Configured task list display within location details
   - Implemented proper task count display

5. **Supporting Utilities:**
   - Created LocationStyling utility to handle conversion between named colors and hex values
   - Implemented geolocation service integration for getting user's current location
   - Added utility functions for map bounds calculation and marker styling

### Mobile App Grocery Management Fixes
1. **Fixed Grocery Item Update Issues:**
   - **API Method Mismatch:** Identified and fixed a mismatch between the mobile app (using PATCH) and the V2 API (expecting PUT) for grocery item updates
   - **Type Handling:** Fixed type conversion issues with the quantity field by ensuring it's always treated as a string in the mobile app
   - **Category Preservation:** Enhanced the `GroceryItem.fromJson` method to properly handle category data when it's returned as a string (API V2) instead of an object
   - **Color Assignment:** Added a helper method to assign appropriate colors to categories based on their names when only the category name is available
   - **Request Formatting:** Updated the `updateGroceryItem` method to ensure category data is sent correctly to the API (extracting just the name when sending to V2 API)

### V2 API Category Management Implementation (Completed)
1. **Fixed Missing V2 API Category Endpoints:**
   - Identified and implemented missing V2 API endpoints for category management:
     - `POST /api/v2/categories` - Create a new category
     - `PUT /api/v2/categories/:id` - Update a category
     - `DELETE /api/v2/categories/:id` - Delete a category
     - `POST /api/v2/categories/merge` - Merge two categories
   - Added proper validation for all endpoints
   - Implemented standardized response format following V2 API conventions
   - Added special handling for predefined categories, allowing color updates while preventing name changes
   - Enhanced error handling with specific error codes and messages

2. **Fixed Mobile App Category Management:**
   - Fixed type casting issue in `category_drawer.dart` when adding subcategories
   - Improved color parsing in `category_form_dialog.dart` to handle MaterialColor and other color formats
   - Enhanced category creation and editing with better error handling
   - Fixed subcategory creation by properly converting CategoryBasic to HierarchicalCategory
   - Added proper handling of predefined categories

### Mobile App Category Filtering Issues (In Progress)
1. **Category Filter Improvements:**
   - Fixed "All tasks" filter to properly reset category selection
   - Attempted to fix "Uncategorized" filter using both `hasCategories=false` and `categories=none` parameters
   - Issue with "Uncategorized" filter remains unresolved - tasks without categories are not being properly filtered
   - Need to investigate API response handling for uncategorized tasks
   - Further investigation needed on backend API handling of uncategorized task filtering

### Mobile App Task Display Fixes
1. **Fixed Missing Reminder Icons and Multiple Categories Display (Backend & Mobile):**
   - **Backend Fix (`taskController.ts` - `getMobileTasks`):** Modified the `getMobileTasks` endpoint to ensure the full `categories` array (with populated `id`, `name`, `color`) is returned for each task in the list view, not just the first category. This was crucial for the mobile app to display all categories.
   - Enhanced TaskItem widget to properly display reminder icons when deadline and reminder are set
   - Updated task model (`TaskListItem.fromJson`, `TaskDetail.fromJson`) to correctly parse and store the full `categories` array from both V1 (task detail) and V2 (task list) API responses. This involved handling various JSON structures for categories (e.g., `categoryObjects`, `categories` array of OIDs, `categories` array of objects).
   - Updated `TaskDetail` model to include `List<Category> categories` and modified `task_form_screen.dart`'s `_initializeForm` to populate selected categories from this list when editing a task.
   - Restructured metadata row display with proper spacing and visual hierarchy
   - Implemented deduplication for categories to prevent repeated display

2. **Improved TaskListItem Model:**
   - Enhanced parsing of task data from multiple API response formats
   - Added better handling of category arrays and objects
   - Fixed reminder detection logic to check multiple field formats
   - Added assumption that tasks with deadlines should display reminder icons
   - Improved error handling and debug logging for task parsing
   - Added support for handling both array and object-based category data from various API versions and endpoints.

3. **Enhanced Task UI Components:**
   - Improved reminder indicator with background color and text for better visibility
   - Created a more robust metadata row that properly handles multiple items
   - Added proper handling of category colors from different formats
   - Enhanced the visual distinction of task metadata elements
   - Fixed layout issues with metadata display on smaller screens
   - Implemented centralized metadata widgets list to improve display control

### Mobile App Task Form and UI Fixes
1. **Fixed Task Form Editing Issues:**
   - Updated `_initializeForm` in `task_form_screen.dart` to properly handle initialization of all task properties, including populating `_selectedCategoryIds` from `task.categories`.
   - Added proper logging for task field initialization during editing
   - Fixed handling of due time, priority, categories, location, and reminders when editing tasks
   - Ensured task detail API data is correctly passed to form fields
   - Improved error handling and validation for all task fields

2. **Fixed Task List Item UI:**
   - Updated `TaskListItemWidget` to display the reminder icon when a task has reminders
   - Modified task list items to display all categories instead of just one
   - Improved layout for multiple categories with proper spacing and wrapping
   - Enhanced UI organization with better section spacing
   - Added proper conditional rendering for metadata sections

3. **Flutter Analyzer Issues:**
   - Fixed critical issues found by Flutter analyzer:
     - Updated `register_screen.dart` to use `Theme.of(context)` instead of direct `AppTheme` access
     - Fixed type issues in `merge_tasks_dialog.dart` and `review_duplicates_dialog.dart` by using `TaskListItem`
     - Corrected `LucideIcons` access in `icon_check.dart` by removing references to non-existent icons
     - Fixed unused imports in various files

### Comprehensive Error Handling Implementation
1. **Implemented Standardized Error Architecture:**
   - Created comprehensive error taxonomy with clear classification
   - Implemented consistent error class hierarchy across all platforms
   - Standardized error response format for improved client handling
   - Developed robust error recovery mechanisms
   - Created testing framework for error handling
   - All error handling tests now passing across the entire application stack

2. **Backend Error Handling Improvements:**
   - Implemented base error classes in `/backend/src/utils/errors.ts`
   - Updated global error handler middleware
   - Created async error handler utility
   - Updated services to use specific error classes
   - Updated controllers to use the async handler pattern

3. **Frontend Error Handling Enhancements:**
   - Created client error classes
   - Implemented error context for centralized management
   - Enhanced API client with standardized error handling
   - Created error notification components
   - Implemented error boundary components
   - Added useSafeAsync hook for simplified async operations

4. **Mobile Error Handling Implementation:**
   - Created AppException classes in Dart
   - Enhanced API client with error handling
   - Implemented error handler providers
   - Created error UI components
   - Integrated error handling in repositories

### Mobile App V2 API Migration Completion
1. **Implemented V2 API Integration:**
   - Updated User model with V2 fields (avatar, preferences)
   - **Ensured V2 Task List API (`/api/v2/tasks`) now returns full category objects for each task.**
   - Enhanced API client with robust token refresh handling
   - Implemented comprehensive test coverage for auth and user endpoints
   - Created UserService for profile operations
   - Standardized error handling across all V2 endpoints

2. **Authentication System Enhancement:**
   - Improved token refresh mechanism
   - Enhanced error handling for auth failures
   - Added comprehensive test coverage
   - Updated API client configuration

3. **User Profile Management:**
   - Implemented profile operations (get, update, delete)
   - Added password change functionality
   - Created comprehensive test suite
   - Enhanced error handling

4. **Testing Implementation:**
   - Created auth_v2_test.dart for authentication endpoints
   - Implemented user_v2_test.dart for user operations
   - Added mock classes for testing
   - Ensured thorough coverage of success and error cases

### Mobile App Groceries Tab Implementation
1. **Implemented Groceries Tab in Mobile App:**
   - Created comprehensive grocery management feature in the Flutter mobile app
   - Implemented models for grocery items and insights with proper JSON parsing
   - Developed a robust grocery service for API interactions
   - Created a state management system using Riverpod for groceries
   - Implemented UI components for displaying and managing grocery items
   - Added support for categorization, filtering, and sorting
   - Integrated with AI suggestions and insights

2. **Grocery Item Management:**
   - Implemented grocery list item widget with check/uncheck functionality
   - Added support for deleting individual items and bulk deletion of checked items
   - Created inline add functionality within categories
   - Implemented edit dialog for modifying grocery items
   - Added category indicators and metadata display

3. **UI and Navigation:**
   - Created a dedicated groceries screen with tabs for current/completed/all items
   - Implemented category grouping with expandable sections
   - Added compact/standard view toggle
   - Implemented bottom action bar with item count and bulk actions
   - Added empty state with suggestions and insights

4. **Backend Integration:**
   - Updated API config with grocery-related endpoints
   - Implemented comprehensive error handling in the grocery service
   - Added support for suggestions and insights API integration
   - Created models that handle different API response formats

### Mobile App AI Insights Implementation
1. **Fixed AI Insights Feature in Mobile App:**
   - Implemented full AI insights feature in the Flutter mobile app
   - Fixed bug with duplicate top bars by centralizing header components
   - Resolved overflow issues by adding proper padding and size constraints
   - Added pagination support for displaying insights
   - Created dedicated insights screen accessible via bottom navigation bar
   - Implemented insight actions for accepting/dismissing recommendations
   - Added task-related action handling from insight cards
   - **Fixed missing V2 API endpoint for generating insights by adding `/api/v2/ai/generate-insights` endpoint to match the mobile app's API configuration**

2. **AI Insights UI Improvements:**
   - Created a card-based insight visualization with context-sensitive styling
   - Implemented priority badges with proper color coding
   - Added task linking from insight cards to task details
   - Included detailed metadata display in insight cards (dates, priorities, etc.)
   - Fixed displaying only limited insights in the dashboard with a "View All" button
   - Added a fixed height container to prevent layout issues
   - Created info cards explaining the AI insights feature to users

3. **Backend Integration:**
   - Properly configured API endpoints in ApiConfig
   - Implemented state management with Riverpod for insights
   - Added proper error handling and loading states
   - Created data models for insights with proper JSON parsing
   - Implemented pagination support with controls for navigating insights
   - **Fixed issue where AI insights would work on first load but fail on refresh due to missing V2 API endpoint**

### Mobile App Task Creation Field Name Fix
1.  **Fixed Task Creation Field Name Mismatches:**
    - Updated field names in `task_form_screen.dart` to properly match the backend API expectations
    - Changed 'description' to 'content', 'dueDate' to 'deadline', 'priorityLevel' to 'priority', and 'categoryIds' to 'categories'
    - Added a String extension method to capitalize priority values (e.g., 'medium' → 'Medium') to match backend expectations
    - Improved validation for required fields, particularly due dates when creating tasks with reminders
    - Implemented proper error handling and user feedback

### Mobile App Task Management Fixes
1.  **Fixed Task Completion Issues:**
    - Modified task_provider.dart to properly handle task completion by delaying UI updates until API call completes
    - Enhanced task_service.dart to add comprehensive error handling and logging for task completion toggles
    - Updated task_list_screen.dart to handle potential errors during task completion toggle
    - Improved task_item.dart to use a callback-based approach for more reliable state updates
2.  **Fixed Task Creation Issues:**
    - Enhanced task_form_screen.dart to validate reminders and require a due date when reminders are set
    - Modified ReminderFormValue class to calculate reminderTime based on dueDate and offset values
    - Improved error handling in task_service.dart to properly process the API response for task creation
    - Updated the task_form_screen.dart to refresh the task list after successful task creation
3.  **Fixed Task Deletion Issues:**
    - Updated task_service.dart to properly handle 204 No Content responses from the backend's DELETE endpoint
    - Enhanced deleteTask method in task_provider.dart to use a non-optimistic update approach (deleting on server first, then updating UI)
    - Added robust type checking and error handling for different API response formats
    - Improved the UI feedback during task deletion with loading indicators and success/error messages
    - Fixed issue where deleted tasks would briefly disappear and then reappear in the task list

### Mobile App Category Management Fixes
1.  **Fixed Subcategory Creation Issue:**
    - Changed field name from 'parentId' to 'parentCategory' in category_form_dialog.dart when creating a subcategory
    - Aligned field names with what the backend controller expects to properly create subcategories
2.  **Improved Category List UI Updates:**
    - Enhanced CategoryService.createCategory() with better logging and response handling
    - Added loading state and detailed logging to CategoryNotifier.createCategory()
    - Implemented .then() callbacks in CategoryDrawer dialog methods to refresh after creation
    - Added auto-expansion of parent categories when subcategories are created
3.  **Remaining API and UI Integration Issues:**
    - Several Color constructor errors identified in category_provider.dart
    - Missing 'put' method in ApiClient class needs implementation

### Mobile App UI Improvements and Feature Enhancements
1.  **Fixed Duplicate Top Bar Issue:**
    - Removed redundant AppBar from TaskListScreen while preserving the main one in DashboardShell
    - Centralized UI elements in DashboardShell (category drawer, filter button)
    - Ensured proper layout with the logout button in the top-right corner
2.  **Task Creation Enhancement:**
    - Updated _showCreateTaskDialog method to use the full task form (context.pushNamed('newTask')) instead of the simplified dialog
    - Provides users with comprehensive task creation options (title, description, due date, priority, categories)
    - Aligns mobile functionality with the web application experience
3.  **Category Filtering Implementation:**
    - Added _showCategoryFilterBottomSheet method in DashboardShell
    - Implemented a filter button in the AppBar that appears specifically on the tasks screen
    - Connected the filter button to the category filter bottom sheet
4.  **Improved Code Organization:**
    - Centralized UI elements and navigation in DashboardShell
    - Removed redundant code from child screens
    - Made the app structure more consistent and maintainable

### Mobile App UI and Authentication Fix
1.  **AppBar UI Enhancement:** Fixed issues with the logout button in the mobile app by:
    - Enhancing AppBar styling with proper elevation and contrasting colors
    - Increasing icon size and adding color to improve visibility
    - Ensuring consistent AppBar layout across different screens
    - Adding debug logging to verify button functionality
2.  **Task Screen Layout Fix:** Removed debug UI elements from TaskListScreen that were conflicting with the AppBar layout
3.  **FloatingActionButton Behavior:** Fixed mismatch between FAB implementation and dialog behavior, ensuring consistent task creation flow

### Flutter Mobile App Authentication Fix
1.  **Corrected API Endpoint Configuration:** Changed `currentUserEndpoint` from `/users/me` to `/api/users/profile` in `api_config.dart` to match the actual backend endpoint.
2.  **Enhanced User Model Robustness:** Updated `User.fromJson` to properly handle different ID field names (`_id` from MongoDB or `id` from normalized API responses).
3.  **Improved Error Handling in AuthService:**
    - Added better logging with more context for troubleshooting.
    - Implemented specific handling for different error types (401/403 auth errors, 404 configuration errors, network issues).
    - Only clearing tokens for actual authentication errors (401/403), not for network or configuration issues.
4.  **Refined AuthNotifier Initialization:**
    - Changed behavior when profile fetch fails to keep the user authenticated if they have a valid token.
    - This prevents users being logged out during temporary network or server issues.
    - Added more detailed error messages to help diagnose issues.

### Flutter Task Loading Fix
1.  **API Response Handling:** Updated `TaskService.getTasks` to correctly parse the backend V2 API response structure `{ data: [], pagination: {} }`.
2.  **Parameter Alignment:** Corrected query parameter names sent to the backend (used `sort`/`order` instead of `sortField`/`sortOrder`).
3.  **Data Model Parsing:** Modified `TaskListItem.fromJson` to robustly handle both `_id` and `id` fields from the API response.
4.  **Logging:** Added more detailed logging in the task service for future debugging.

### Authentication System Completion (Mobile)
1.  **Single Source of Truth:** Solidified `AuthProvider` (Riverpod) as the exclusive manager of authentication state.
2.  **State Management:** Utilized `AuthProvider` state directly in UI components (`LoginScreen`) removing local state duplication.
3.  **Routing Logic:** Implemented robust redirect logic in `GoRouter` based on `AuthProvider`'s `isLoading` and `isAuthenticated` states, ensuring correct navigation (Splash -> Login/Dashboard).
4.  **Error Handling:** Implemented explicit error clearing before auth attempts and consistent error display using snackbars fed from `AuthProvider` state.
5.  **Logging:** Added detailed logging throughout the authentication flow (AuthProvider, Router) for improved debugging.

### Mobile App Implementation
1. Flutter Mobile App Architecture
   - Implemented core service classes for task and authentication
   - Created a robust API client with token management
   - Designed UI components following Material Design principles
   - Structured the app using feature-based organization
   - Fixed connectivity issues with platform-specific URL configuration

2. Mobile-Specific Considerations
   - Implemented mobile-optimized API (v2) with reduced payload size
   - Enhanced error handling for mobile network conditions
   - Added platform detection for proper backend URL configuration
   - Designed responsive UI layouts for various device sizes
   - Setup secure token storage with flutter_secure_storage

3. Network Connectivity Architecture
   - Implemented platform-specific URL selection for different environments
   - Successfully addressed web browser vs. Android emulator connectivity differences
   - Configured backend with regex-based CORS support for dynamic ports
   - Streamlined app initialization by removing complex manual network testing

### Authentication Implementation (General - Backend/Web)
1. Dual Authentication Flow
   - Enhanced authentication system to support both web and mobile clients
   - Implemented JWT-based authentication for mobile
   - Maintained cookie-based auth for web application
   - Added token refresh with rotation for better security

2. Security Measures
   - Implemented rate limiting for API endpoints
   - Enhanced CORS configuration for mobile clients
   - Added CSRF protection with bypassing for mobile token-based requests
   - Improved security headers configuration

3. Error Handling
   - Standardized error responses with specific codes
   - Enhanced logging for authentication failures
   - Added proper validation for token formats
   - Improved error visibility in the client applications

## Active Considerations

### Mobile Development
- Feature parity between web and mobile apps
- Flutter platform-specific UI adaptations
- Testing across different Android versions/devices
- Mobile-specific performance optimization
- Battery and data usage considerations
- Resolving any remaining non-auth linter errors
- **(New)** Mobile app distribution using Firebase App Distribution with direct CLI approach (not the Flutter plugin) for more reliable builds and deployments
- **(Resolved)** Ensuring consistent data model handling (e.g., `id` vs `_id`, and full category arrays) between Flutter and backend for both task list and task detail views.
- **(Resolved)** Ensuring task list items properly display all metadata (categories, reminders) by fixing both frontend parsing and backend API responses.
- **(Resolved)** Maintaining consistent UI component styling and visibility across app sections
- **(Resolved)** Unified navigation patterns across the mobile app
- **(Resolved)** Streamlined user experience in task management and filtering
- **(Resolved)** Field name consistency between frontend and backend APIs
- **(Resolved)** Addressing missing Color constructor and API method implementations
- **(Resolved)** Ensuring proper error handling and UI state updates for tasks
- **(Resolved)** Implementing proper validation for task creation with reminders
- **(Resolved)** Implementing AI insights pagination and task action handling consistently
- **(Resolved)** Ensuring task list items properly display all metadata (categories, reminders)
- **(Resolved)** Fixing Flutter analyzer issues to maintain code quality

### Authentication & Security
- **(Completed)** Balance between security and user experience in the implemented flow.
- **(Completed)** Token expiration times and rotation strategy (if applicable to mobile token strategy).
- **(Ongoing)** Rate limiting thresholds review.
- **(Ongoing)** CORS configuration review for multiple client types.
- **(Ongoing)** Handling API versioning for mobile and web clients.

### Performance
- Token validation overhead
- Redis integration impact
- Database query performance optimization
- Mobile API response times
- Service worker initialization impact on page load
- **Pagination implementation impact on large data sets in mobile**
- **Date parsing robustness and error visibility**

### Error Handling
- **(Completed)** Mobile-specific error recovery strategies
- **(Completed)** Offline error handling approaches
- **(Completed)** Error presentation in limited mobile UI space
- **(Completed)** Cross-platform error code standardization
- **(Completed)** Improved visibility for AI date parsing failures

## Next Actions
1.  **(Completed)** Resolve auth-related linter errors in the Flutter mobile codebase.
2.  **(Completed)** Fix logout button visibility issues in mobile app.
3.  **(Completed)** Address duplicate top bar and improve task creation flow in mobile app.
4.  **(Completed)** Fix category management issues in the mobile app (subcategory creation and list updates).
5.  **(Completed)** Fix task completion and creation issues in the mobile app.
6.  **(Completed)** Implement AI insights feature in the mobile app.
7.  **(Completed)** Add comprehensive error handling to mobile services.
8.  **(Completed)** Fix task editing form to properly display all task properties.
9.  **(Completed)** Fix task list item UI to properly display all categories and reminder icons.
10. **(Completed)** Resolve critical Flutter analyzer issues affecting the project.
11. **(Completed)** Consolidate the two APIs into one using the v2 API to maintain feature parity between the frontend web-app and the mobile-app.
12. **(Completed)** Fix mobile app login issues after API consolidation.
13. **(Completed)** Improve date parsing robustness and visibility in AI-generated tasks.
14. **(Ongoing)** Address remaining Color constructor errors and implement missing API methods.
15. **(Ongoing)** Complete implementation of remaining mobile app features.
16. **(Ongoing)** Implement optimized state management for mobile app.
17. **(Crucial)** Test completed authentication flows thoroughly across web and mobile platforms.
18. **(Crucial)** Verify task loading, creation, and completion are stable after recent fixes.
19. **(If Applicable)** Finalize Redis integration for token management (if part of mobile strategy).
20. **(If Applicable)** Implement token blacklisting (if part of mobile strategy).
21. **(Ongoing)** Set up cross-platform security monitoring.
22. **(Ongoing)** Document API endpoints for mobile development.
23. **(New)** Set up Firebase App Distribution with proper service account authentication and direct CLI approach for mobile app testing.
24. **(Ongoing)** Create deployment pipeline for mobile app releases.
25. **(Completed)** Test AI insights feature thoroughly, including all insight types and actions.
26. **(Completed)** Test mobile task display after UI fixes to ensure reminder icons and multiple categories display correctly (both in list and form).
27. **(Crucial)** Thoroughly test all API endpoints after the consolidation to ensure they work correctly for both web and mobile clients.
28. **(New)** Fix CSRF protection test failures identified during API consolidation testing.
29. **(New)** Fix admin route test failures identified during API consolidation testing.
30. **(New)** Replace print statements with proper logging in the mobile app.
31. **(New)** Monitor date parsing failures during alpha testing to identify common problematic date formats.

## Active Issues
- **Mobile Linter Errors:** Several Color constructor errors in category_provider.dart and missing 'put' method in ApiClient class.
- **Mobile App Feature Completion:** Basic connectivity, Auth, **Task Loading, Creation, Completion, and Editing** are working, but feature implementation needs to continue.
- **Authentication Refinement:** Backend token rotation and blacklisting might still be pending.
- **TypeScript Errors:** Several TypeScript errors exist in the backend codebase.
- **Test Coverage:** Backend and mobile app test coverage remains low, **especially for the new auth flow and task management features.**
- **API Consolidation Test Failures:** Several test failures related to CSRF protection and admin routes need to be addressed.
- **Mobile App Logging:** The mobile app uses print statements for logging, which should be replaced with a proper logging framework.
- **(Resolved)** Mobile App Feature Completion: Basic connectivity, Auth, Task Loading (with multiple categories), Creation, Completion, and Editing are working.
- **(Resolved)** API Consolidation: Successfully consolidated the two APIs into one using the v2 API.
- **(Resolved)** Mobile App Login: Fixed login issues in the mobile app after API consolidation.
- **(Resolved)** AI Date Parsing: Enhanced date parsing robustness and visibility of parsing failures in AI-generated tasks.
- **(Resolved)** Mobile AI Insights API: Fixed API integration for AI insights in the mobile app after API consolidation, resolving 404 errors with PATCH/PUT requests.

## Recent Changes
- **(New)** Fixed mobile app AI insights API integration after API consolidation:
  - **Issue:** Mobile app was encountering 404 errors when attempting to use insights endpoints
  - **Root Cause:** The API consolidation changed how insight action endpoints work:
    - V1 used `PATCH /api/ai/insights/:id` but V2 expects `POST /api/ai/insights/:id/action`
  - **Fix:**
    - Updated the AI service to use POST instead of PATCH for insight actions
    - Changed endpoint URL to include `/action` suffix
    - Updated API config to correctly define the action endpoint
    - Added documentation to help with future API integration
  - This fixes the AI insights feature in the mobile app, allowing users to accept/dismiss insights and perform related actions (changing deadlines, marking tasks complete, etc.).
- **(New)** Enhanced date parsing robustness and visibility for AI-generated tasks:
  - **Backend:**
    - Improved the `parseDateString` function in `aiController.ts` with detailed warning logs
    - Added metadata field `aiSuggestedDeadlineFailed` to the Task model to store unparsable date strings
    - Enhanced the `processQuickAdd` function to track date parsing failures
  - **Frontend:**
    - Added a visual indicator (amber warning triangle) in task-item.tsx when a deadline parsing failure occurs
    - Implemented tooltips showing the original unparsable date string
    - Updated the edit-task-dialog to display a warning about failed deadline parsing
  - This improves visibility during alpha testing and helps collect data on problematic date formats.
- **(New)** Fixed multi-category display issues in mobile app (task list and task edit form):
  - **Backend:** Modified `getMobileTasks` in `taskController.ts` to return the full populated `categories` array for each task in the list.
  - **Mobile:**
    - Updated `TaskListItem.fromJson` and `TaskDetail.fromJson` to correctly parse the full `categories` array from API responses (handling `categoryObjects`, `categories` array of OIDs, and `categories` array of full objects).
    - Added `List<Category> categories` to `TaskDetail` model.
    - Updated `task_form_screen.dart` (`_initializeForm`) to use `task.categories` for populating selected categories.
    - Ensured `TaskItem` widget iterates over `task.categories` to display all category badges.
- **(New)** Fixed task editing UI issues:
  - Updated task form to properly initialize all fields when editing a task
  - Fixed task list items to show all categories and reminder icons
  - Improved task form validation, especially for reminders requiring due dates
  - Enhanced error handling and logging during task editing
  - Added more descriptive debug logs to help track issues
  - Ensured proper handling of due time, priority, categories, and location

## Project Insights
- **(New)** Comprehensive task metadata display (multiple categories, reminder status) helps users better understand and manage their tasks at a glance.
- **(New)** Careful handling of various API response formats is crucial for proper data display, especially when integrating with evolving backend services.
- **(New)** Visual distinction of different metadata types (categories, reminders, priorities) through color and layout improves usability and recognition speed.
- **(New)** Debug logging throughout the data parsing process is invaluable for diagnosing and fixing display issues in complex applications.
- **(New)** Proper data field mapping between UI forms and API endpoints is crucial - small discrepancies like different field names ('dueDate' vs 'deadline', 'description' vs 'content') can break functionality.
- **(New)** API responses for list views vs. detail views can have subtle differences (e.g., how related objects like categories are embedded or populated). Clients need to be robust enough to handle these or APIs need to be strictly consistent.
- **(New)** When debugging UI display issues for collections (like multiple categories), it's crucial to inspect the data at each stage: raw API response, model parsing, and widget rendering.

### Mobile App Task Filtering Implementation
1. **Implemented Comprehensive Task Filtering:**
   - Created a `TaskFilters` model class in Dart that matches the web app's filtering options
   - Updated the `TaskProvider` to use this new filtering model
   - Implemented a new `TaskFilterSheet` component with support for multiple filter criteria:
     - Task status (completed/incomplete)
     - Priority levels (low, medium, high, critical)
     - Categories with multi-select functionality
     - Date ranges with field selection (deadline, created, updated)
     - Reminder filters (has deadline, has reminder)
     - Boolean filters (AI generated, has dependencies, is blocking)
   - Updated the `CategoryFilterSheet` to work with the new filters model
   - Modified the dashboard shell to use the new filter sheet
   - Updated the `TaskService` to handle all filter parameters

2. **Backend API Integration:**
   - Enhanced the V2 `taskController.ts` to properly handle all filter parameters:
     - Added support for date range filtering with field selection
     - Implemented reminder filtering logic
     - Added AI generated task filtering
     - Added dependency relationship filtering (has dependencies, is blocking)
   - Implemented proper logging for filter parameters
   - Fixed the backend to ensure all categories are properly returned for tasks

3. **Method Compatibility:**
   - Added backward compatibility aliases for methods to maintain existing code:
     - `toggleTaskCompletion` as an alias for `toggleCompletion`
     - `updatePageSize` to maintain pagination functionality

4. **Testing and Fixes:**
   - Tested all filter combinations to ensure proper functionality
   - Fixed issues with some filters not working (date range, reminders, AI generated)
   - Validated filter behavior matches the web application

# Active Context

## Current Focus
- Deployment stability and build process optimization
- Package management standardization
- Clean separation of development and production environments

## Recent Changes
1. Build Configuration
   - Modified TypeScript configuration to exclude test files
   - Optimized production builds by removing unnecessary dependencies
   - Standardized on Yarn for package management

2. Dependency Management
   - Removed frontend-only dependencies from backend
   - Cleaned up package manager conflicts
   - Properly separated dev and production dependencies

## Active Decisions
1. Package Management
   - Using Yarn exclusively
   - Removed package-lock.json
   - Keeping frontend and backend dependencies strictly separated

2. Build Process
   - Test files excluded from production builds
   - Development dependencies not included in production
   - Type definitions properly organized

## Project Insights
1. Build Environment
   - Render runs in production mode
   - Need to carefully manage dependencies based on environment
   - Type definitions required for compilation must be in production dependencies

2. Package Management
   - Importance of consistent package manager usage
   - Need to avoid mixing npm and yarn
   - Clear separation between frontend and backend packages

## Next Actions
1. Monitor deployment stability
2. Consider implementing deployment tests
3. Review and update documentation
4. Add license field to root package.json (optional)

## Current Patterns
1. Dependency Management
   - Runtime dependencies in `dependencies`
   - Development tools in `devDependencies`
   - Type definitions needed for compilation in `dependencies`
   - Test-related packages in `devDependencies`

2. Build Process
   - Clean separation of test and production code
   - Optimized TypeScript configuration
   - Environment-aware dependency management

## June 2024: Mobile Build System & Dependency Upgrades
- Upgraded Firebase dependencies in `pubspec.yaml` to latest compatible versions: `firebase_core`, `firebase_auth`, `firebase_messaging`.
- Increased `minSdkVersion` to 23 in `android/app/build.gradle.kts` to support new Firebase plugins.
- Updated Kotlin Gradle plugin to `2.1.20` in `android/settings.gradle.kts` to resolve metadata version mismatch with Firebase libraries.
- AGP (Android Gradle Plugin) version is `8.7.0`.
- Focus: Ensuring build compatibility with latest Firebase and Kotlin ecosystem for Flutter mobile.
- Next: Confirm successful build and test app functionality.

## 2024-05-20: Google Login Mobile Fix
- Issue: Google login on mobile failed due to audience mismatch ("payload audience != requiredAudience").
- Cause: The backend was only accepting a single Google client ID, but the mobile app could use different IDs (Android, iOS, Web).
- Solution: Backend updated to accept a comma-separated list of client IDs via `GOOGLE_CLIENT_IDS` env variable. Code supports both `GOOGLE_CLIENT_ID` and `GOOGLE_CLIENT_IDS` for backward compatibility.
- Troubleshooting: Decoded the ID token to check the `aud` claim, ensured it matched the backend env, and restarted the backend after updating env variables.
- Result: Google login now works on mobile after backend restart.
