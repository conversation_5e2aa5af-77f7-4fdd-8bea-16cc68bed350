// mobile/flashtasks_mobile/lib/src/features/settings/screens/settings_screen.dart
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:lucide_icons/lucide_icons.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../providers/settings_providers.dart';
import '../../../shared/widgets/error_display.dart';
import '../../../shared/widgets/loading_indicator.dart';

/// Main settings screen with navigation to sub-screens
class SettingsScreen extends ConsumerWidget {
  const SettingsScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final settingsState = ref.watch(userSettingsProvider);
    final theme = Theme.of(context);

    // Show loading indicator if loading and no settings available yet
    if (settingsState.isLoading && settingsState.settings == null) {
      return const Scaffold(
        body: Center(
          child: LoadingIndicator(),
        ),
      );
    }

    // Show error if there's an error and no settings available
    if (settingsState.error != null && settingsState.settings == null) {
      return Scaffold(
        body: Center(
          child: ErrorDisplay(
            message: 'Failed to load settings: ${settingsState.error}',
            onRetry: () => ref.read(userSettingsProvider.notifier).refreshSettings(),
          ),
        ),
      );
    }

    return Scaffold(
      body: RefreshIndicator(
        onRefresh: () => ref.read(userSettingsProvider.notifier).refreshSettings(),
        child: ListView(
          children: [
            _buildSectionHeader(context, 'Account'),
            _buildSettingsItem(
              context,
              icon: LucideIcons.user,
              title: 'Profile',
              subtitle: 'Manage your name and email',
              onTap: () => context.pushNamed('profileSettings'),
            ),
            _buildSettingsItem(
              context,
              icon: LucideIcons.lock,
              title: 'Security',
              subtitle: 'Change password, manage account deletion',
              onTap: () => context.pushNamed('accountSettings'),
            ),
            _buildSectionHeader(context, 'Application'),
            _buildSettingsItem(
              context,
              icon: LucideIcons.listChecks,
              title: 'Task Settings',
              subtitle: 'Default sorting, deadlines, reminders',
              onTap: () => context.pushNamed('taskSettings'),
            ),
            _buildSettingsItem(
              context,
              icon: LucideIcons.shoppingCart,
              title: 'Grocery Preferences',
              subtitle: 'Default list, quick-add behavior, shared lists',
              onTap: () => context.pushNamed('groceryPreferences'),
            ),
            _buildSettingsItem(
              context,
              icon: LucideIcons.bell,
              title: 'Notification Settings',
              subtitle: 'Email, push, and in-app notifications',
              onTap: () => context.pushNamed('notificationSettings'),
            ),
            _buildSettingsItem(
              context,
              icon: LucideIcons.calendarClock,
              title: 'Date & Time',
              subtitle: 'Formats and timezone preferences',
              onTap: () => context.pushNamed('dateTimeSettings'),
            ),
            _buildSectionHeader(context, 'AI & Automation'),
            _buildSettingsItem(
              context,
              icon: LucideIcons.tag,
              title: 'Keytag Mappings',
              subtitle: 'Map keywords to categories automatically',
              onTap: () => context.pushNamed('keytagMappingSettings'),
            ),
            _buildSettingsItem(
              context,
              icon: LucideIcons.mapPin,
              title: 'Category-Location Mappings',
              subtitle: 'Assign locations based on categories',
              onTap: () => context.pushNamed('categoryLocationMappingSettings'),
            ),
            _buildSettingsItem(
              context,
              icon: LucideIcons.locateFixed,
              title: 'Location Suggestions',
              subtitle: 'Configure location recommendation accuracy',
              onTap: () => context.pushNamed('locationSuggestionSettings'),
            ),
            _buildSectionHeader(context, 'Developer'),
            _buildSettingsItem(
              context,
              icon: LucideIcons.server,
              title: 'Server Settings',
              subtitle: 'Configure server connection for physical devices',
              onTap: () => context.pushNamed('serverSettings'),
            ),
            // Add some padding at the bottom
            const SizedBox(height: 24),
          ],
        ),
      ),
    );
  }

  /// Build a section header with consistent styling
  Widget _buildSectionHeader(BuildContext context, String title) {
    return Padding(
      padding: const EdgeInsets.fromLTRB(16, 24, 16, 8),
      child: Text(
        title,
        style: Theme.of(context).textTheme.titleMedium?.copyWith(
          color: Theme.of(context).colorScheme.primary,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }

  /// Build a settings item with consistent styling
  Widget _buildSettingsItem(
    BuildContext context, {
    required IconData icon,
    required String title,
    required VoidCallback onTap,
    String? subtitle,
  }) {
    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
      elevation: 0,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: BorderSide(
          color: Theme.of(context).colorScheme.outline.withOpacity(0.1),
        ),
      ),
      child: ListTile(
        contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
        leading: Icon(
          icon,
          color: Theme.of(context).colorScheme.primary,
        ),
        title: Text(
          title,
          style: Theme.of(context).textTheme.titleMedium,
        ),
        subtitle: subtitle != null
          ? Text(
              subtitle,
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: Theme.of(context).colorScheme.onSurfaceVariant,
              ),
            )
          : null,
        trailing: Icon(
          LucideIcons.chevronRight,
          size: 20,
          color: Theme.of(context).colorScheme.onSurfaceVariant,
        ),
        onTap: onTap,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
      ),
    );
  }
}
