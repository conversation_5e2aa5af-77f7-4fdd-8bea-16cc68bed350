import 'package:dio/dio.dart';
import '../config/api_config.dart';
import '../../features/settings/models/grocery_preferences.dart';

/// Service for managing grocery preferences
class GroceryPreferencesService {
  final Dio _apiClient;

  GroceryPreferencesService({required Dio apiClient}) : _apiClient = apiClient;

  /// Get grocery preferences for the current user
  Future<GroceryPreferences> getGroceryPreferences() async {
    try {
      final response = await _apiClient.get('${ApiConfig.settingsEndpoint}/grocery-preferences');
      
      if (response.statusCode == 200 && response.data['success'] == true) {
        return GroceryPreferences.fromJson(response.data['data']);
      }
      
      throw Exception(response.data?['error']?['message'] ?? 'Failed to get grocery preferences');
    } catch (e) {
      print('[GroceryPreferencesService] Error getting preferences: $e');
      
      // Return default preferences if API call fails
      return const GroceryPreferences();
    }
  }

  /// Update grocery preferences
  Future<GroceryPreferences> updateGroceryPreferences(UpdateGroceryPreferencesData preferences) async {
    try {
      final response = await _apiClient.put(
        '${ApiConfig.settingsEndpoint}/grocery-preferences',
        data: preferences.toJson(),
      );
      
      if (response.statusCode == 200 && response.data['success'] == true) {
        return GroceryPreferences.fromJson(response.data['data']);
      }
      
      throw Exception(response.data?['error']?['message'] ?? 'Failed to update grocery preferences');
    } catch (e) {
      print('[GroceryPreferencesService] Error updating preferences: $e');
      throw Exception('Failed to update grocery preferences: $e');
    }
  }

  /// Set default list to personal
  Future<GroceryPreferences> setDefaultToPersonal() async {
    return updateGroceryPreferences(
      const UpdateGroceryPreferencesData(
        defaultListType: 'personal',
        defaultSharedListOwnerId: null,
      ),
    );
  }

  /// Set default list to a shared list
  Future<GroceryPreferences> setDefaultToSharedList(String listOwnerId) async {
    return updateGroceryPreferences(
      UpdateGroceryPreferencesData(
        defaultListType: 'shared',
        defaultSharedListOwnerId: listOwnerId,
      ),
    );
  }

  /// Toggle auto switch to default list
  Future<GroceryPreferences> toggleAutoSwitchToDefault(bool enabled) async {
    return updateGroceryPreferences(
      UpdateGroceryPreferencesData(
        autoSwitchToDefault: enabled,
      ),
    );
  }

  /// Toggle show personal list in sidebar
  Future<GroceryPreferences> toggleShowPersonalListInSidebar(bool show) async {
    return updateGroceryPreferences(
      UpdateGroceryPreferencesData(
        showPersonalListInSidebar: show,
      ),
    );
  }
}
