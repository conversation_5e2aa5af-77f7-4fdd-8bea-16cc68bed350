import { Request, Response, NextFunction } from 'express';
import mongoose, { Types } from 'mongoose';
import { GroceryItem, IGroceryItem } from '../models/GroceryItem';
import { UserGroceryLibraryItem } from '../models/UserGroceryLibraryItem';
import { GroceryList } from '../models/GroceryList';
import { GroceryListInvitation } from '../models/GroceryListInvitation';
import { User } from '../models/User';
import { categorizeGroceryItem } from '../services/groceryCategoryService';
import { socketService } from '../services/socketService';
import { sendEmail } from '../services/emailService';
import secureLogger from '../utils/secureLogger';
import crypto from 'crypto';

// Helper function to update the user's grocery library
const updateGroceryLibrary = async (userId: Types.ObjectId, itemNames: string[]) => {
  if (!itemNames || itemNames.length === 0) return;

  const normalizedNames = itemNames.map(name => name.trim().toLowerCase()).filter(name => name.length > 0);
  if (normalizedNames.length === 0) return;

  secureLogger.log(`Updating grocery library for user ${userId} with items:`, normalizedNames);
  try {
    // For each item, get category information
    for (const name of normalizedNames) {
      // Get or determine the category for this item
      const categoryData = await categorizeGroceryItem(name);

      // Update the library item with the category
      await UserGroceryLibraryItem.findOneAndUpdate(
        { userId, itemName: name },
        {
          $inc: { addFrequency: 1 },
          $set: {
            lastAddedAt: new Date(),
            category: {
              name: categoryData.name,
              confidence: categoryData.confidence
            }
          }
        },
        { upsert: true } // Create if it doesn't exist
      );
    }

    secureLogger.log(`Grocery library updated for user ${userId} with categorization`);
  } catch (error) {
    secureLogger.error(`Error updating grocery library for user ${userId}:`, error);
    // Don't block the main grocery operation if library update fails
  }
};

// Helper function to capitalize item names - shared function
const capitalizeItemName = (name: string): string => {
  return name
    .split(' ')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1))
    .join(' ');
};

const groceryController = {
  // Get all grocery items for the user (supports shared lists)
  getGroceryItems: async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    const userId = req.user?.userId;
    const { listOwnerId } = req.query; // For accessing shared lists

    if (!userId) {
      res.status(401).json({
        success: false,
        error: {
          message: 'User not authenticated',
          code: 'AUTHENTICATION_REQUIRED'
        }
      });
      return;
    }

    try {
      let targetUserId = userId;

      // If accessing a shared list
      if (listOwnerId && listOwnerId !== userId) {
        // Verify user has access to this shared list
        const groceryList = await GroceryList.findOne({
          userId: listOwnerId,
          isShared: true
        });

        if (!groceryList || !groceryList.hasAccess(new mongoose.Types.ObjectId(userId))) {
          res.status(403).json({
            success: false,
            error: {
              message: 'You do not have access to this grocery list',
              code: 'ACCESS_DENIED'
            }
          });
          return;
        }

        targetUserId = listOwnerId as string;
      }

      const items = await GroceryItem.find({ userId: targetUserId })
        .populate('addedBy', 'name email')
        .populate('checkedBy', 'name email')
        .populate('lastModifiedBy', 'name email')
        .sort({ isChecked: 1, createdAt: -1 }); // Sort by unchecked first, then newest

      res.status(200).json({
        success: true,
        data: items
      });
    } catch (error) {
      secureLogger.error(`Error fetching grocery items for user ${userId}:`, error);
      next(error);
    }
  },

  // Add new grocery items (handles bulk addition, supports shared lists)
  addGroceryItems: async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    const userId = req.user?.userId;
    const { listOwnerId } = req.query; // For adding to shared lists

    if (!userId) {
      res.status(401).json({
        success: false,
        error: {
          message: 'User not authenticated',
          code: 'AUTHENTICATION_REQUIRED'
        }
      });
      return;
    }

    const { items } = req.body;
    if (!items || !Array.isArray(items) || items.length === 0) {
      res.status(400).json({
        success: false,
        error: {
          message: 'No valid items provided',
          code: 'VALIDATION_ERROR'
        }
      });
      return;
    }

    try {
      let targetUserId = userId;
      let groceryList = null;

      // If adding to a shared list
      if (listOwnerId && listOwnerId !== userId) {
        // Verify user has edit access to this shared list
        groceryList = await GroceryList.findOne({
          userId: listOwnerId,
          isShared: true
        });

        if (!groceryList || !groceryList.canEdit(new mongoose.Types.ObjectId(userId))) {
          res.status(403).json({
            success: false,
            error: {
              message: 'You do not have permission to add items to this grocery list',
              code: 'ACCESS_DENIED'
            }
          });
          return;
        }

        targetUserId = listOwnerId as string;
      }

      secureLogger.log(`Adding grocery items for user ${userId} to list ${targetUserId}:`, { itemCount: items.length });

      // Process each item to ensure it has required fields
      const processedItems: IGroceryItem[] = [];

      for (const item of items) {
        // Check for minimum required properties
        if (!item.name || typeof item.name !== 'string' || item.name.trim() === '') {
          continue; // Skip invalid items
        }

        // Capitalize the item name
        const capitalizedName = capitalizeItemName(item.name.trim());

        // Get category using AI if not provided
        let category = item.category;
        if (!category || !category.name) {
          const categoryData = await categorizeGroceryItem(capitalizedName);
          category = {
            name: categoryData.name,
            color: categoryData.color
          };
        }

        // Create new item
        const newItem = new GroceryItem({
          userId: new mongoose.Types.ObjectId(targetUserId),
          name: capitalizedName,
          isChecked: item.isChecked ?? false,
          category,
          notes: item.notes,
          quantity: item.quantity,
          addedBy: targetUserId !== userId ? new mongoose.Types.ObjectId(userId) : undefined,
        });

        const savedItem = await newItem.save();
        processedItems.push(savedItem);
      }

      // Update grocery library in background (fire and forget)
      if (processedItems.length > 0) {
        const itemNames = processedItems.map(item => item.name);
        updateGroceryLibrary(new mongoose.Types.ObjectId(targetUserId), itemNames)
          .catch(err => secureLogger.error(`Background grocery library update failed:`, err));
      }

      // If adding to a shared list, emit real-time updates and update activity
      if (groceryList && targetUserId !== userId) {
        for (const item of processedItems) {
          socketService.emitGroceryItemAdded(
            targetUserId,
            item.toObject(),
            new mongoose.Types.ObjectId(userId)
          );
        }

        // Update list activity
        groceryList.lastCollaborativeActivity = new Date();
        await groceryList.save();
      }

      // Return saved items
      res.status(201).json({
        success: true,
        data: {
          items: processedItems
        }
      });
    } catch (error) {
      secureLogger.error(`Error adding grocery items for user ${userId}:`, error);
      next(error);
    }
  },

  // Update a grocery item (e.g., toggle isChecked)
  updateGroceryItem: async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    const userId = req.user?.userId;
    const itemId = req.params.id;
    const { isChecked, name, category, notes, quantity } = req.body;

    if (!userId) {
      res.status(401).json({
        success: false,
        error: {
          message: 'User not authenticated',
          code: 'AUTHENTICATION_REQUIRED'
        }
      });
      return;
    }
    if (!itemId || !mongoose.Types.ObjectId.isValid(itemId)) {
      res.status(400).json({
        success: false,
        error: {
          message: 'Valid Grocery Item ID required',
          code: 'INVALID_ID_FORMAT'
        }
      });
      return;
    }

    const updates: Partial<IGroceryItem> = {};
    if (typeof isChecked === 'boolean') {
      updates.isChecked = isChecked;
    }
    if (typeof name === 'string' && name.trim().length > 0) {
      updates.name = name.trim();

      // If name changed, try to update category as well
      try {
        const categoryData = await categorizeGroceryItem(name.trim());
        updates.category = {
          name: categoryData.name,
          color: categoryData.color
        };
      } catch (err) {
        secureLogger.error(`Error categorizing renamed item "${name}":`, err);
        // Continue without category update if it fails
      }
    }
    if (category) {
      updates.category = category;
    }
    if (notes !== undefined) {
      updates.notes = notes;
    }
    if (quantity !== undefined) {
      updates.quantity = quantity;
    }

    if (Object.keys(updates).length === 0) {
      res.status(400).json({
        success: false,
        error: {
          message: 'No valid update data provided',
          code: 'VALIDATION_ERROR'
        }
      });
      return;
    }

    try {
      // First, find the item to check if it's in a shared list
      const existingItem = await GroceryItem.findById(itemId);

      if (!existingItem) {
        res.status(404).json({
          success: false,
          error: {
            message: 'Grocery item not found',
            code: 'RESOURCE_NOT_FOUND'
          }
        });
        return;
      }

      // Check permissions - either owner or collaborator with edit access
      let groceryList = null;
      if (existingItem.userId.toString() !== userId) {
        // Check if this is a shared list and user has edit access
        groceryList = await GroceryList.findOne({
          userId: existingItem.userId,
          isShared: true
        });

        if (!groceryList || !groceryList.canEdit(new mongoose.Types.ObjectId(userId))) {
          res.status(403).json({
            success: false,
            error: {
              message: 'You do not have permission to edit this grocery list item',
              code: 'FORBIDDEN_OPERATION'
            }
          });
          return;
        }

        // Add tracking fields for collaborative edits
        updates.lastModifiedBy = new mongoose.Types.ObjectId(userId);
        if (typeof isChecked === 'boolean') {
          updates.checkedBy = new mongoose.Types.ObjectId(userId);
          updates.checkedAt = new Date();
        }
      }

      // Update the item
      const updatedItem = await GroceryItem.findByIdAndUpdate(
        itemId,
        { $set: updates },
        { new: true, runValidators: true }
      ).populate('addedBy', 'name email')
       .populate('checkedBy', 'name email')
       .populate('lastModifiedBy', 'name email');

      if (!updatedItem) {
        res.status(404).json({
          success: false,
          error: {
            message: 'Failed to update grocery item',
            code: 'RESOURCE_NOT_FOUND'
          }
        });
        return;
      }

      // If it's a shared list item, emit real-time update and update list activity
      if (groceryList) {
        socketService.emitGroceryItemUpdated(
          updatedItem.userId.toString(),
          updatedItem.toObject(),
          new mongoose.Types.ObjectId(userId)
        );

        // Update list activity
        groceryList.lastCollaborativeActivity = new Date();
        await groceryList.save();
      }

      res.status(200).json({
        success: true,
        data: updatedItem
      });
    } catch (error) {
      secureLogger.error(`Error updating grocery item ${itemId} for user ${userId}:`, error);
      next(error);
    }
  },

  // Delete a specific grocery item
  deleteGroceryItem: async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    const userId = req.user?.userId;
    const itemId = req.params.id;

    if (!userId) {
      res.status(401).json({
        success: false,
        error: {
          message: 'User not authenticated',
          code: 'AUTHENTICATION_REQUIRED'
        }
      });
      return;
    }
    if (!itemId || !mongoose.Types.ObjectId.isValid(itemId)) {
      res.status(400).json({
        success: false,
        error: {
          message: 'Valid Grocery Item ID required',
          code: 'INVALID_ID_FORMAT'
        }
      });
      return;
    }

    try {
      const deletedItem = await GroceryItem.findOneAndDelete({ _id: itemId, userId: userId });
      if (!deletedItem) {
        res.status(404).json({
          success: false,
          error: {
            message: 'Grocery item not found or user not authorized',
            code: 'RESOURCE_NOT_FOUND'
          }
        });
        return;
      }
      res.status(204).send(); // No content on successful delete
    } catch (error) {
      secureLogger.error(`Error deleting grocery item ${itemId} for user ${userId}:`, error);
      next(error);
    }
  },

  // Delete all checked grocery items
  deleteCheckedGroceryItems: async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    const userId = req.user?.userId;

    if (!userId) {
      res.status(401).json({
        success: false,
        error: {
          message: 'User not authenticated',
          code: 'AUTHENTICATION_REQUIRED'
        }
      });
      return;
    }

    try {
      const result = await GroceryItem.deleteMany({ userId: userId, isChecked: true });
      secureLogger.log(`Deleted ${result.deletedCount} checked grocery items for user ${userId}`);
      res.status(200).json({
        success: true,
        data: { deletedCount: result.deletedCount }
      });
    } catch (error) {
      secureLogger.error(`Error deleting checked grocery items for user ${userId}:`, error);
      next(error);
    }
  },

  // Get grocery suggestions based on user's library frequency
  getGrocerySuggestions: async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    const userId = req.user?.userId;
    if (!userId) {
      res.status(401).json({
        success: false,
        error: {
          message: 'User not authenticated',
          code: 'AUTHENTICATION_REQUIRED'
        }
      });
      return;
    }

    // Define how many suggestions to return (e.g., top 5)
    const limit = parseInt(req.query.limit as string) || 5;
    const listOwnerId = req.query.listOwnerId as string; // Optional context for shared lists

    try {
      // Determine which user's library to use for suggestions
      let targetUserId = userId;
      if (listOwnerId) {
        // Validate that the user has access to this shared list
        const groceryList = await GroceryList.findOne({ userId: listOwnerId });
        if (groceryList && groceryList.isShared) {
          const userRole = groceryList.getUserRole(userId);
          if (userRole === 'editor' || userRole === 'owner') {
            targetUserId = listOwnerId;
            secureLogger.log(`Suggestions: Using shared list owner's library ${targetUserId} (user role: ${userRole})`);
          } else {
            secureLogger.warn(`Suggestions: User ${userId} does not have access to shared list ${listOwnerId}`);
          }
        }
      }

      // Find items in the target user's library, sort by frequency (desc) and last added (desc as tie-breaker)
      const suggestions = await UserGroceryLibraryItem.find({ userId: targetUserId })
        .sort({ addFrequency: -1, lastAddedAt: -1 })
        .limit(limit)
        .select('itemName -_id'); // Select only the item name

      // Extract just the names
      const suggestionNames = suggestions.map(s => s.itemName);

      // Filter out items already on the target user's current grocery list
      const currentListItems = await GroceryItem.find({ userId: targetUserId, isChecked: false }).select('name');
      const currentListNames = currentListItems.map(item => item.name.toLowerCase());
      const filteredSuggestions = suggestionNames.filter(name => !currentListNames.includes(name.toLowerCase()));

      res.status(200).json({
        success: true,
        data: filteredSuggestions
      });
    } catch (error) {
      secureLogger.error(`Error fetching grocery suggestions for user ${userId}:`, error);
      next(error);
    }
  },

  // Endpoint to update categories for existing items
  updateCategories: async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    const userId = req.user?.userId;
    if (!userId) {
      res.status(401).json({
        success: false,
        error: {
          message: 'User not authenticated',
          code: 'AUTHENTICATION_REQUIRED'
        }
      });
      return;
    }

    try {
      // Get all uncategorized items for this user
      const items = await GroceryItem.find({
        userId,
        $or: [
          { category: { $exists: false } },
          { category: null },
          { 'category.name': { $exists: false } },
          { 'category.name': null },
          { 'category.name': '' }
        ]
      });

      if (items.length === 0) {
        res.status(200).json({
          success: true,
          data: {
            message: 'No uncategorized items found',
            updated: 0
          }
        });
        return;
      }

      let updateCount = 0;

      // Update each item with a category
      for (const item of items) {
        try {
          const categoryData = await categorizeGroceryItem(item.name);

          item.category = {
            name: categoryData.name,
            color: categoryData.color
          };

          await item.save();
          updateCount++;
        } catch (err) {
          secureLogger.error(`Error categorizing item "${item.name}":`, err);
          // Continue with other items
        }
      }

      res.status(200).json({
        success: true,
        data: {
          message: `Updated categories for ${updateCount} items`,
          updated: updateCount,
          total: items.length
        }
      });
    } catch (error) {
      secureLogger.error(`Error updating categories for user ${userId}:`, error);
      next(error);
    }
  },

  // Get grocery insights
  getGroceryInsights: async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    const userId = req.user?.userId;
    if (!userId) {
      res.status(401).json({
        success: false,
        error: {
          message: 'User not authenticated',
          code: 'AUTHENTICATION_REQUIRED'
        }
      });
      return;
    }

    try {
      let lastShoppingTrip: Date | null = null;
      let mostFrequentItem: string | null = null;
      let topCategory: { name: string; percentage: number } | null = null;

      // 1. Find the last shopping trip (approximated by last completed item update)
      const lastCompletedItem = await GroceryItem.findOne(
        { userId, isChecked: true },
        { updatedAt: 1 }, // Select only updatedAt field
        { sort: { updatedAt: -1 } } // Sort by newest update first
      );
      if (lastCompletedItem) {
        lastShoppingTrip = lastCompletedItem.updatedAt;
      }

      // 2. Find the most frequent item from the library
      const frequentItem = await UserGroceryLibraryItem.findOne(
        { userId },
        { itemName: 1 }, // Select only itemName
        { sort: { addFrequency: -1 } } // Sort by highest frequency
      );
      if (frequentItem) {
        mostFrequentItem = capitalizeItemName(frequentItem.itemName);
      }

      // 3. Find the top category from the library
      const libraryItems = await UserGroceryLibraryItem.find({ userId });
      if (libraryItems.length > 0) {
        const categoryCounts: Record<string, number> = {};
        let totalCategorized = 0;

        libraryItems.forEach(item => {
          if (item.category?.name) {
            categoryCounts[item.category.name] = (categoryCounts[item.category.name] || 0) + 1;
            totalCategorized++;
          }
        });

        if (totalCategorized > 0) {
          let maxCount = 0;
          let topCatName = '';
          for (const [name, count] of Object.entries(categoryCounts)) {
            if (count > maxCount) {
              maxCount = count;
              topCatName = name;
            }
          }
          topCategory = {
            name: topCatName,
            percentage: Math.round((maxCount / totalCategorized) * 100),
          };
        }
      }

      // Average shopping interval is too complex for now, omitting.

      res.status(200).json({
        success: true,
        data: {
          lastShoppingTrip,
          mostFrequentItem,
          topCategory,
          // avgShoppingInterval: null, // Omitted for now
        }
      });

    } catch (error) {
      secureLogger.error(`Error fetching grocery insights for user ${userId}:`, error);
      next(error);
    }
  },

  // Search user's grocery library for typeahead
  searchGroceryLibrary: async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    const userId = req.user?.userId;
    if (!userId) {
      res.status(401).json({
        success: false,
        error: {
          message: 'User not authenticated',
          code: 'AUTHENTICATION_REQUIRED'
        }
      });
      return;
    }

    const query = req.query.q as string;
    if (!query || typeof query !== 'string' || query.trim().length === 0) {
      // Return empty array if query is missing or invalid
      res.status(200).json({
        success: true,
        data: []
      });
      return;
    }

    const searchTerm = query.trim().toLowerCase();
    const limit = parseInt(req.query.limit as string) || 5; // Default limit 5

    try {
      // Search for items starting with the query, case-insensitive
      const results = await UserGroceryLibraryItem.find(
        {
          userId: userId,
          itemName: { $regex: `^${searchTerm}`, $options: 'i' } // Case-insensitive prefix search
        },
        { itemName: 1, _id: 0 } // Select only itemName, exclude _id
      )
      .sort({ addFrequency: -1, lastAddedAt: -1 }) // Sort by frequency/recency
      .limit(limit);

      // Extract just the names and capitalize them
      const suggestionNames = results.map(item => capitalizeItemName(item.itemName));

      res.status(200).json({
        success: true,
        data: suggestionNames
      });
    } catch (error) {
      secureLogger.error(`Error searching grocery library for user ${userId} with query "${query}":`, error);
      next(error);
    }
  },



  // ===== COLLABORATION METHODS =====

  // Get grocery list metadata
  getGroceryList: async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    const userId = req.user?.userId;
    if (!userId) {
      res.status(401).json({
        success: false,
        error: {
          message: 'User not authenticated',
          code: 'AUTHENTICATION_REQUIRED'
        }
      });
      return;
    }

    try {
      let groceryList = await GroceryList.findOne({ userId })
        .populate('collaborators.userId', 'name email')
        .populate('collaborators.invitedBy', 'name email');

      // Create grocery list if it doesn't exist
      if (!groceryList) {
        groceryList = new GroceryList({
          userId: new mongoose.Types.ObjectId(userId),
          isShared: false,
          collaborators: [],
          shareSettings: {
            allowCollaboratorInvites: false,
            requireApprovalForEdits: false,
            notifyOnChanges: true,
          }
        });
        await groceryList.save();
      }

      // Add user role for frontend
      const groceryListWithRole = {
        ...groceryList.toObject(),
        userRole: 'owner'
      };

      res.status(200).json({
        success: true,
        data: groceryListWithRole
      });
    } catch (error) {
      secureLogger.error(`Error fetching grocery list for user ${userId}:`, error);
      next(error);
    }
  },

  // Enable sharing for grocery list
  enableSharing: async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    const userId = req.user?.userId;
    const { shareSettings } = req.body;

    if (!userId) {
      res.status(401).json({
        success: false,
        error: {
          message: 'User not authenticated',
          code: 'AUTHENTICATION_REQUIRED'
        }
      });
      return;
    }

    try {
      let groceryList = await GroceryList.findOne({ userId });

      if (!groceryList) {
        // Create grocery list if it doesn't exist
        groceryList = new GroceryList({
          userId: new mongoose.Types.ObjectId(userId),
          isShared: true,
          collaborators: [],
          shareSettings
        });
      } else {
        groceryList.isShared = true;
        groceryList.shareSettings = shareSettings;
      }

      await groceryList.save();
      await groceryList.populate('collaborators.userId', 'name email');
      await groceryList.populate('collaborators.invitedBy', 'name email');

      // Add user role for frontend
      const groceryListWithRole = {
        ...groceryList.toObject(),
        userRole: 'owner'
      };

      res.status(200).json({
        success: true,
        data: groceryListWithRole
      });

      secureLogger.log(`User ${userId} enabled sharing for their grocery list`);
    } catch (error) {
      secureLogger.error(`Error enabling sharing for user ${userId}:`, error);
      next(error);
    }
  },

  // Disable sharing for grocery list
  disableSharing: async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    const userId = req.user?.userId;

    if (!userId) {
      res.status(401).json({
        success: false,
        error: {
          message: 'User not authenticated',
          code: 'AUTHENTICATION_REQUIRED'
        }
      });
      return;
    }

    try {
      const groceryList = await GroceryList.findOne({ userId });

      if (!groceryList) {
        res.status(404).json({
          success: false,
          error: {
            message: 'Grocery list not found',
            code: 'RESOURCE_NOT_FOUND'
          }
        });
        return;
      }

      groceryList.isShared = false;
      groceryList.collaborators = []; // Remove all collaborators
      await groceryList.save();

      // Add user role for frontend
      const groceryListWithRole = {
        ...groceryList.toObject(),
        userRole: 'owner'
      };

      res.status(200).json({
        success: true,
        data: groceryListWithRole
      });

      secureLogger.log(`User ${userId} disabled sharing for their grocery list`);
    } catch (error) {
      secureLogger.error(`Error disabling sharing for user ${userId}:`, error);
      next(error);
    }
  },

  // Update share settings
  updateShareSettings: async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    const userId = req.user?.userId;
    const { shareSettings } = req.body;

    if (!userId) {
      res.status(401).json({
        success: false,
        error: {
          message: 'User not authenticated',
          code: 'AUTHENTICATION_REQUIRED'
        }
      });
      return;
    }

    try {
      const groceryList = await GroceryList.findOne({ userId });

      if (!groceryList) {
        res.status(404).json({
          success: false,
          error: {
            message: 'Grocery list not found',
            code: 'RESOURCE_NOT_FOUND'
          }
        });
        return;
      }

      groceryList.shareSettings = { ...groceryList.shareSettings, ...shareSettings };
      await groceryList.save();
      await groceryList.populate('collaborators.userId', 'name email');
      await groceryList.populate('collaborators.invitedBy', 'name email');

      // Add user role for frontend
      const groceryListWithRole = {
        ...groceryList.toObject(),
        userRole: 'owner'
      };

      res.status(200).json({
        success: true,
        data: groceryListWithRole
      });

      secureLogger.log(`User ${userId} updated share settings for their grocery list`);
    } catch (error) {
      secureLogger.error(`Error updating share settings for user ${userId}:`, error);
      next(error);
    }
  },

  // Add collaborator to grocery list
  addCollaborator: async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    const userId = req.user?.userId;
    const { email, role = 'editor' } = req.body;

    if (!userId) {
      res.status(401).json({
        success: false,
        error: {
          message: 'User not authenticated',
          code: 'AUTHENTICATION_REQUIRED'
        }
      });
      return;
    }

    try {
      // First check if any grocery list exists for this user
      const groceryList = await GroceryList.findOne({ userId });

      if (!groceryList) {
        res.status(404).json({
          success: false,
          error: {
            message: 'Grocery list not found. Please enable sharing first.',
            code: 'GROCERY_LIST_NOT_FOUND'
          }
        });
        return;
      }

      // Check if sharing is enabled
      if (!groceryList.isShared) {
        res.status(400).json({
          success: false,
          error: {
            message: 'Sharing is not enabled for this grocery list. Please enable sharing first.',
            code: 'SHARING_NOT_ENABLED'
          }
        });
        return;
      }

      // Find user by email
      const collaboratorUser = await User.findOne({ email: email.toLowerCase() });
      if (!collaboratorUser) {
        res.status(404).json({
          success: false,
          error: {
            message: 'User with this email not found',
            code: 'USER_NOT_FOUND'
          }
        });
        return;
      }

      // Check if user is trying to add themselves
      if ((collaboratorUser._id as mongoose.Types.ObjectId).toString() === userId) {
        res.status(400).json({
          success: false,
          error: {
            message: 'Cannot add yourself as a collaborator',
            code: 'INVALID_OPERATION'
          }
        });
        return;
      }

      // Add collaborator
      await groceryList.addCollaborator(
        collaboratorUser._id as mongoose.Types.ObjectId,
        role,
        new mongoose.Types.ObjectId(userId)
      );

      await groceryList.populate('collaborators.userId', 'name email');
      await groceryList.populate('collaborators.invitedBy', 'name email');

      // Send notification email
      try {
        const inviter = await User.findById(userId);
        await sendEmail(
          email,
          'You\'ve been added to a grocery list',
          `${inviter?.name || 'Someone'} has added you as a ${role} to their grocery list. You can now view and ${role === 'editor' ? 'edit' : 'view'} their grocery items.`,
          `<p>${inviter?.name || 'Someone'} has added you as a ${role} to their grocery list.</p><p>You can now view and ${role === 'editor' ? 'edit' : 'view'} their grocery items.</p>`
        );
      } catch (emailError) {
        secureLogger.error('Failed to send collaborator notification email:', emailError);
        // Don't fail the request if email fails
      }

      // Add user role for frontend
      const groceryListWithRole = {
        ...groceryList.toObject(),
        userRole: 'owner'
      };

      res.status(200).json({
        success: true,
        data: {
          list: groceryListWithRole,
          addedCollaborator: {
            userId: collaboratorUser._id,
            name: collaboratorUser.name,
            email: collaboratorUser.email,
            role
          }
        }
      });

      secureLogger.log(`User ${userId} added ${email} as ${role} to their grocery list`);
    } catch (error) {
      if (error instanceof Error && error.message.includes('already a collaborator')) {
        res.status(409).json({
          success: false,
          error: {
            message: 'User is already a collaborator on this list',
            code: 'ALREADY_EXISTS'
          }
        });
        return;
      }
      secureLogger.error(`Error adding collaborator for user ${userId}:`, error);
      next(error);
    }
  },

  // Remove collaborator from grocery list
  removeCollaborator: async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    const userId = req.user?.userId;
    const { collaboratorId } = req.params;

    if (!userId) {
      res.status(401).json({
        success: false,
        error: {
          message: 'User not authenticated',
          code: 'AUTHENTICATION_REQUIRED'
        }
      });
      return;
    }

    try {
      const groceryList = await GroceryList.findOne({ userId, isShared: true });

      if (!groceryList) {
        res.status(404).json({
          success: false,
          error: {
            message: 'Shared grocery list not found',
            code: 'RESOURCE_NOT_FOUND'
          }
        });
        return;
      }

      await groceryList.removeCollaborator(new mongoose.Types.ObjectId(collaboratorId));
      await groceryList.populate('collaborators.userId', 'name email');
      await groceryList.populate('collaborators.invitedBy', 'name email');

      // Add user role for frontend
      const groceryListWithRole = {
        ...groceryList.toObject(),
        userRole: 'owner'
      };

      res.status(200).json({
        success: true,
        data: groceryListWithRole
      });

      secureLogger.log(`User ${userId} removed collaborator ${collaboratorId} from their grocery list`);
    } catch (error) {
      if (error instanceof Error && error.message.includes('not a collaborator')) {
        res.status(404).json({
          success: false,
          error: {
            message: 'User is not a collaborator on this list',
            code: 'RESOURCE_NOT_FOUND'
          }
        });
        return;
      }
      secureLogger.error(`Error removing collaborator for user ${userId}:`, error);
      next(error);
    }
  },

  // Get shared grocery lists where user is a collaborator
  getSharedLists: async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    const userId = req.user?.userId;

    if (!userId) {
      res.status(401).json({
        success: false,
        error: {
          message: 'User not authenticated',
          code: 'AUTHENTICATION_REQUIRED'
        }
      });
      return;
    }

    try {
      // Find lists where user is either:
      // 1. A collaborator on someone else's shared list
      // 2. The owner of a shared list
      const sharedLists = await GroceryList.find({
        $or: [
          {
            'collaborators.userId': new mongoose.Types.ObjectId(userId),
            isShared: true
          },
          {
            userId: new mongoose.Types.ObjectId(userId),
            isShared: true
          }
        ]
      })
        .populate('userId', 'name email')
        .populate('collaborators.userId', 'name email')
        .populate('collaborators.invitedBy', 'name email')
        .sort({ lastCollaborativeActivity: -1 });

      // Add user role for each list
      const listsWithRole = sharedLists.map(list => {
        const userRole = list.getUserRole(new mongoose.Types.ObjectId(userId));
        return {
          ...list.toObject(),
          userRole
        };
      });

      secureLogger.log(`Found ${listsWithRole.length} shared lists for user ${userId}`);

      res.status(200).json({
        success: true,
        data: listsWithRole
      });
    } catch (error) {
      secureLogger.error(`Error fetching shared lists for user ${userId}:`, error);
      next(error);
    }
  },

  // Get pending invitations for the current user
  getPendingInvitations: async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    const userId = req.user?.userId;

    if (!userId) {
      res.status(401).json({
        success: false,
        error: {
          message: 'User not authenticated',
          code: 'AUTHENTICATION_REQUIRED'
        }
      });
      return;
    }

    try {
      const user = await User.findById(userId);
      if (!user) {
        res.status(404).json({
          success: false,
          error: {
            message: 'User not found',
            code: 'USER_NOT_FOUND'
          }
        });
        return;
      }

      const invitations = await GroceryListInvitation.find({
        inviteeEmail: user.email.toLowerCase(),
        status: 'pending'
      })
        .populate('groceryListId')
        .populate('inviterUserId', 'name email')
        .sort({ createdAt: -1 });

      res.status(200).json({
        success: true,
        data: invitations
      });
    } catch (error) {
      secureLogger.error(`Error fetching pending invitations for user ${userId}:`, error);
      next(error);
    }
  },

  // Accept an invitation to collaborate on a grocery list
  acceptInvitation: async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    const userId = req.user?.userId;
    const { token } = req.params;

    if (!userId) {
      res.status(401).json({
        success: false,
        error: {
          message: 'User not authenticated',
          code: 'AUTHENTICATION_REQUIRED'
        }
      });
      return;
    }

    try {
      const invitation = await GroceryListInvitation.findOne({
        token,
        status: 'pending'
      }).populate('groceryListId');

      if (!invitation) {
        res.status(404).json({
          success: false,
          error: {
            message: 'Invitation not found or already processed',
            code: 'INVITATION_NOT_FOUND'
          }
        });
        return;
      }

      if (invitation.isExpired()) {
        invitation.status = 'expired';
        await invitation.save();
        res.status(410).json({
          success: false,
          error: {
            message: 'Invitation has expired',
            code: 'INVITATION_EXPIRED'
          }
        });
        return;
      }

      const groceryList = await GroceryList.findById(invitation.groceryListId);
      if (!groceryList) {
        res.status(404).json({
          success: false,
          error: {
            message: 'Grocery list not found',
            code: 'RESOURCE_NOT_FOUND'
          }
        });
        return;
      }

      // Add user as collaborator
      await groceryList.addCollaborator(
        new mongoose.Types.ObjectId(userId),
        invitation.role,
        invitation.inviterUserId
      );

      // Mark invitation as accepted
      invitation.status = 'accepted';
      invitation.acceptedAt = new Date();
      invitation.inviteeUserId = new mongoose.Types.ObjectId(userId);
      await invitation.save();

      await groceryList.populate('userId', 'name email');
      await groceryList.populate('collaborators.userId', 'name email');
      await groceryList.populate('collaborators.invitedBy', 'name email');

      // Add user role for frontend
      const groceryListWithRole = {
        ...groceryList.toObject(),
        userRole: invitation.role
      };

      res.status(200).json({
        success: true,
        data: groceryListWithRole
      });

      secureLogger.log(`User ${userId} accepted invitation ${token}`);
    } catch (error) {
      secureLogger.error(`Error accepting invitation for user ${userId}:`, error);
      next(error);
    }
  },

  // Decline an invitation to collaborate on a grocery list
  declineInvitation: async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    const userId = req.user?.userId;
    const { token } = req.params;

    if (!userId) {
      res.status(401).json({
        success: false,
        error: {
          message: 'User not authenticated',
          code: 'AUTHENTICATION_REQUIRED'
        }
      });
      return;
    }

    try {
      const invitation = await GroceryListInvitation.findOne({
        token,
        status: 'pending'
      });

      if (!invitation) {
        res.status(404).json({
          success: false,
          error: {
            message: 'Invitation not found or already processed',
            code: 'INVITATION_NOT_FOUND'
          }
        });
        return;
      }

      // Mark invitation as declined
      invitation.status = 'declined';
      await invitation.save();

      res.status(200).json({
        success: true,
        data: { message: 'Invitation declined successfully' }
      });

      secureLogger.log(`User ${userId} declined invitation ${token}`);
    } catch (error) {
      secureLogger.error(`Error declining invitation for user ${userId}:`, error);
      next(error);
    }
  }
};

export default groceryController;
